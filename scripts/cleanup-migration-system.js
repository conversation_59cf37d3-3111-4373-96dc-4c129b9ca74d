#!/usr/bin/env node

/**
 * Migration System Cleanup Script
 * Implements the recommendations from MIGRATION_SYSTEM_ANALYSIS.md
 * Archives old migration files and updates the system structure
 */

import { existsSync, mkdirSync, renameSync, unlinkSync, writeFileSync, readFileSync } from 'fs'
import { join, dirname } from 'path'

class MigrationSystemCleanup {
  constructor() {
    this.projectRoot = process.cwd()
    this.backupDir = join(this.projectRoot, 'server/database/backups/migration-tools')
    this.changes = []
  }

  async cleanup() {
    console.log('🧹 Starting Migration System Cleanup...\n')
    
    try {
      // Step 1: Create backup directory
      this.createBackupDirectory()
      
      // Step 2: Archive migration files
      this.archiveMigrationFiles()
      
      // Step 3: Archive old test file
      this.archiveOldTestFile()
      
      // Step 4: Update migration registry
      this.updateMigrationRegistry()
      
      // Step 5: Generate cleanup report
      this.generateCleanupReport()
      
      console.log('\n🎉 Migration system cleanup completed successfully!')
      console.log('\nSummary of changes:')
      this.changes.forEach(change => console.log(`   ${change}`))
      
    } catch (error) {
      console.error('❌ Cleanup failed:', error.message)
      process.exit(1)
    }
  }

  createBackupDirectory() {
    console.log('📁 Creating backup directory...')
    
    if (!existsSync(this.backupDir)) {
      mkdirSync(this.backupDir, { recursive: true })
      this.changes.push('✅ Created backup directory: server/database/backups/migration-tools/')
    } else {
      console.log('   Directory already exists')
    }
  }

  archiveMigrationFiles() {
    console.log('📦 Archiving migration files...')
    
    const migrationFiles = [
      {
        source: 'server/utils/migration/types/AuthorsMigration.js',
        target: 'AuthorsMigration.js',
        description: 'Authors migration (Firebase to D1)'
      },
      {
        source: 'server/utils/migration/types/ReferencesMigration.js', 
        target: 'ReferencesMigration.js',
        description: 'References migration (Firebase to D1)'
      },
      {
        source: 'server/utils/migration/types/QuotesMigration.js',
        target: null, // Delete this one as it's incomplete
        description: 'Incomplete quotes migration (DELETE)'
      }
    ]

    migrationFiles.forEach(file => {
      const sourcePath = join(this.projectRoot, file.source)
      
      if (existsSync(sourcePath)) {
        if (file.target) {
          // Archive the file
          const targetPath = join(this.backupDir, file.target)
          renameSync(sourcePath, targetPath)
          this.changes.push(`📦 Archived: ${file.source} → migration-tools/${file.target}`)
        } else {
          // Delete the file
          unlinkSync(sourcePath)
          this.changes.push(`🗑️  Deleted: ${file.source} (incomplete implementation)`)
        }
      } else {
        console.log(`   ⚠️  File not found: ${file.source}`)
      }
    })
  }

  archiveOldTestFile() {
    console.log('🧪 Archiving old test file...')
    
    const oldTestFile = join(this.projectRoot, 'scripts/test-import-system.js')
    const archivedTestFile = join(this.backupDir, 'legacy-test-import-system.js')
    
    if (existsSync(oldTestFile)) {
      renameSync(oldTestFile, archivedTestFile)
      this.changes.push('📦 Archived: scripts/test-import-system.js → migration-tools/legacy-test-import-system.js')
    } else {
      console.log('   ⚠️  Old test file not found')
    }
  }

  updateMigrationRegistry() {
    console.log('📋 Updating migration registry...')
    
    const registryPath = join(this.projectRoot, 'server/utils/migration/core/MigrationRegistry.js')
    
    if (existsSync(registryPath)) {
      try {
        let registryContent = readFileSync(registryPath, 'utf-8')
        
        // Comment out the archived migration imports and registrations
        const updates = [
          {
            search: "const { ReferencesMigration } = await import('../types/ReferencesMigration.js')",
            replace: "// const { ReferencesMigration } = await import('../types/ReferencesMigration.js') // ARCHIVED"
          },
          {
            search: "const { AuthorsMigration } = await import('../types/AuthorsMigration.js')",
            replace: "// const { AuthorsMigration } = await import('../types/AuthorsMigration.js') // ARCHIVED"
          },
          {
            search: "this.register('references', ReferencesMigration)",
            replace: "// this.register('references', ReferencesMigration) // ARCHIVED"
          },
          {
            search: "this.register('authors', AuthorsMigration)",
            replace: "// this.register('authors', AuthorsMigration) // ARCHIVED"
          }
        ]
        
        let updated = false
        updates.forEach(update => {
          if (registryContent.includes(update.search)) {
            registryContent = registryContent.replace(update.search, update.replace)
            updated = true
          }
        })
        
        if (updated) {
          writeFileSync(registryPath, registryContent)
          this.changes.push('📝 Updated: MigrationRegistry.js (commented out archived migrations)')
        }
        
      } catch (error) {
        console.log(`   ⚠️  Failed to update registry: ${error.message}`)
      }
    } else {
      console.log('   ⚠️  Migration registry not found')
    }
  }

  generateCleanupReport() {
    console.log('📄 Generating cleanup report...')
    
    const report = {
      timestamp: new Date().toISOString(),
      cleanup_type: 'migration-system-cleanup',
      summary: {
        files_archived: this.changes.filter(c => c.includes('📦')).length,
        files_deleted: this.changes.filter(c => c.includes('🗑️')).length,
        files_updated: this.changes.filter(c => c.includes('📝')).length,
        directories_created: this.changes.filter(c => c.includes('✅')).length
      },
      changes: this.changes,
      recommendations_implemented: [
        'Archived AuthorsMigration.js and ReferencesMigration.js',
        'Deleted incomplete QuotesMigration.js',
        'Archived legacy test-import-system.js',
        'Updated MigrationRegistry.js to comment out archived migrations',
        'Created migration-tools backup directory'
      ],
      next_steps: [
        'Use scripts/test-export-import-roundtrip.js for testing',
        'Consider exporting current database for production onboarding data',
        'Update onboarding system to use new export format when ready',
        'Implement version-based migrations for future schema changes'
      ]
    }
    
    const reportPath = join(this.backupDir, 'cleanup-report-' + new Date().toISOString().replace(/[:.]/g, '-') + '.json')
    writeFileSync(reportPath, JSON.stringify(report, null, 2))
    
    // Also create a README in the backup directory
    const readmePath = join(this.backupDir, 'README.md')
    const readmeContent = `# Migration Tools Archive

This directory contains archived migration tools and legacy files from the verbatims project.

## Archived Files

### Migration Classes
- **AuthorsMigration.js**: Firebase-to-D1 authors migration (completed)
- **ReferencesMigration.js**: Firebase-to-D1 references migration (completed)

### Legacy Tests
- **legacy-test-import-system.js**: Original import system tests (replaced by test-export-import-roundtrip.js)

## Why These Files Were Archived

1. **Migration Complete**: The Firebase-to-D1 migration was successfully completed
2. **No Dependencies**: The onboarding system uses separate, optimized transformation logic
3. **System Cleanup**: Streamlining the codebase after Firebase cleanup
4. **Better Testing**: New comprehensive test suite provides better coverage

## Current System

- **Import/Export**: Uses /admin/import and /admin/export endpoints
- **Testing**: scripts/test-export-import-roundtrip.js provides comprehensive testing
- **Onboarding**: server/api/onboarding/database.post.ts handles initial data loading

## Future Use

These files are kept for:
- Historical reference
- Potential future migrations
- Understanding the original Firebase data structure
- Debugging legacy data issues

Last updated: ${new Date().toISOString()}
`
    
    writeFileSync(readmePath, readmeContent)
    
    this.changes.push(`📄 Generated cleanup report: ${reportPath}`)
    this.changes.push(`📄 Created README: ${readmePath}`)
  }
}

// Run cleanup if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const cleanup = new MigrationSystemCleanup()
  cleanup.cleanup()
}

export { MigrationSystemCleanup }
