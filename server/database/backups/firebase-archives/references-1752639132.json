{"meta": {"format": "JSON", "version": "1.1.0", "projectId": "memorare-98eee", "resourcePath": ["references"], "recursive": false, "creationTime": 1752639137, "app": "firefoo"}, "data": {"13CuhT9VBMzQLYBJ1xdB": {"created_at": {"__time__": "2023-10-21T13:59:26.248Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-10-21T13:52:39.888Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Three Thousand Years of Longing", "release": {"before_common_era": false, "original": {"__time__": "2022-05-19T22:00:00.000Z"}}, "summary": "Three Thousand Years of Longing is a 2022 fantasy romantic drama film directed and produced by <PERSON>. Written by <PERSON> and <PERSON>, it is based on the 1994 short story \"The Djinn in the Nightingale's Eye\" by <PERSON><PERSON> <PERSON><PERSON> and stars <PERSON><PERSON><PERSON> as a djinn who is unleashed from a bottle by a professor (<PERSON><PERSON><PERSON>) and tells her stories from his thousands of years of existence. The film is dedicated to <PERSON>'s mother <PERSON>, as well as <PERSON><PERSON>, relative of producer <PERSON>.", "type": {"primary": "film", "secondary": "Fantasy romantic drama"}, "updated_at": {"__time__": "2023-10-21T13:59:26.248Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThree Thousand Years of Longing-1697896773706.jpg?alt=media", "imageName": "Three Thousand Years of Longing-1697896773706.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Three_Thousand_Years_of_Longing", "youtube": "http://www.youtube.com/watch?v=oHDAwvxeAoc"}, "__collections__": {}}, "1Q1qGsvVGxPRjsZddRjs": {"created_at": {"__time__": "2020-12-23T12:12:54.365Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Marriage of Heaven and Hell", "release": {"before_common_era": false, "original": {"__time__": "1793-12-31T23:50:39.000Z"}}, "summary": "The Marriage of Heaven and Hell is a book by the English poet and printmaker <PERSON>. It is a series of texts written in imitation of biblical prophecy but expressing <PERSON>'s own intensely personal Romantic and revolutionary beliefs.", "summary_localized": {}, "type": {"primary": "Book", "secondary": "Poetry"}, "type_localized": {}, "updated_at": {"__time__": "2020-12-23T12:12:54.365Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Marriage of Heaven and Hell-1608725577602.jpg?alt=media", "image_name": "The Marriage of Heaven and Hell-1608725577602.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Marriage_of_Heaven_and_Hell", "youtube": ""}, "__collections__": {}}, "1oUoRTFgk2q1kDKTL7ab": {"created_at": {"__time__": "2023-10-21T13:09:17.734Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-10-21T12:50:43.299Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Fall of the House of Usher", "release": {"before_common_era": false, "original": {"__time__": "2023-10-11T22:00:00.000Z"}}, "summary": "The Fall of the House of Usher is an American gothic horror drama television miniseries created by <PERSON>. Loosely based on the short story of the same name and other works by <PERSON>, it premiered on Netflix with all eight episodes released at once on October 12, 2023.\n\nThe story is about the CEO of a corrupt pharmaceutical company faces his questionable past when his children start dying in mysterious and brutal ways.", "type": {"primary": "tv_series", "secondary": "Ghotic horror"}, "updated_at": {"__time__": "2023-10-21T13:09:17.734Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Fall of the House of Usher-1697893764051.jpg?alt=media", "imageName": "The Fall of the House of Usher-1697893764051.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81414665", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Fall_of_the_House_of_Usher_(miniseries)", "youtube": ""}, "__collections__": {}}, "1qnphy8FlZdjgwUM5e3c": {"created_at": {"__time__": "2020-10-25T12:47:51.731Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Better Ideas", "release": {"before_common_era": false, "original": {"__time__": "2017-01-22T23:00:00.000Z"}}, "summary": "Better ideas is a YouTube channel about life improvement, work optimisation and social among other topics. It's hosted by <PERSON>.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Lifestyle"}, "type_localized": {}, "updated_at": {"__time__": "2020-10-25T12:47:52.000Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/groups/179287156102224/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBetter Ideas-1603630075114.jpg?alt=media", "image_name": "Better Ideas-1603630075114.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/joeysch<PERSON>tz", "website": "https://www.patreon.com/betterideastv/creators", "wikipedia": "", "youtube": "https://www.youtube.com/channel/UCtUId5WFnN82GdDy7DgaQ7w"}, "__collections__": {}}, "1uvgEhJzURkrgXayn8Bi": {"created_at": {"__time__": "2020-02-01T22:11:00.334Z"}, "id": "1uvgEhJzURkrgXayn8Bi", "image": {"credits": {"artist": "", "before_common_era": false, "company": "Siecle Digital", "date": {"__time__": "2020-02-20T23:00:00.000Z"}, "location": "", "name": "Twitter facilite la gestion des threads", "url": "https://thumbor.sd-cdn.fr/XGQl-HjvKJo_BQfIjfIdrYhf1CI=/fit-in/1938x1090/cdn.sd-cdn.fr/wp-content/uploads/2019/03/Twitter-logo-illustration.jpg"}}, "language": "fr", "links": [], "name": "<PERSON><PERSON>'s tweet (2013-12-10)", "release": {"before_common_era": false, "original": {"__time__": "2013-12-09T23:00:00.000Z"}}, "summary": "A poetic tweet from the artist <PERSON><PERSON>.", "summary_localized": {}, "type": {"primary": "post", "secondary": "Poetry"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.334Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F-<PERSON><PERSON><PERSON>'s tweet (2013-12-10)-1616029594241.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/Bouletcorp/status/******************?s=20", "website": "https://twitter.com/Bouletcorp/status/******************?s=20", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "23T97fwtKVFGwI6JJcSW": {"created_at": {"__time__": "2023-09-25T10:00:23.990Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-09-25T09:34:26.315Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Voyage au pays des maths", "release": {"before_common_era": false, "original": {"__time__": "2021-09-22T22:00:00.000Z"}}, "summary": "\"Voyage au pays des maths\" is an Arte animated mini-serie documentary created by <PERSON> ni 2021. This documentary explores mathematical concepts and explains them in simple words.\n\nA season 2 is in production as of 2023.", "type": {"primary": "video", "secondary": "Sciences"}, "updated_at": {"__time__": "2023-09-25T10:00:23.991Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FVoyage au pays des maths-1695636047828.png?alt=media", "imageName": "Voyage au pays des maths-1695636047828.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.arte.tv/fr/videos/RC-021426/voyages-au-pays-des-maths/", "wikipedia": "https://fr.wikipedia.org/wiki/Voyages_au_pays_des_maths", "youtube": "https://www.youtube.com/playlist?list=PLCwXWOyIR22veT31gK5JwmqxuVc0Uoy8a"}, "__collections__": {}}, "3Z3omOSwiEIsPLD78JF2": {"created_at": {"__time__": "2024-01-19T21:54:53.917Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-01T16:55:36.723Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Le cinéma américain par ses auteurs", "release": {"before_common_era": false, "original": {"__time__": "1977-07-31T22:00:00.000Z"}}, "summary": "A book written by <PERSON>.", "type": {"primary": "book", "secondary": "Cinema"}, "updated_at": {"__time__": "2024-01-19T21:54:53.917Z"}, "urls": {"amazon": "https://www.amazon.fr/Cin%C3%A9ma-am%C3%A9ricain-par-ses-auteurs/dp/B0014MCT5C", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe cinéma américain par ses auteurs-1705701299960.jpg?alt=media", "imageName": "Le cinéma américain par ses auteurs-1705701299960.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "3spYFqU51WrMhlHEabSI": {"created_at": {"__time__": "2020-02-01T22:11:00.338Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Internet est mort. (et vous aussi)", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Technology"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.338Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "primevideo": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": "https://youtu.be/vOB-56n4XCc"}, "__collections__": {}}, "4FOkT4Du7kNeVrNxUFgi": {"created_at": {"__time__": "2020-01-31T23:00:00.000Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "<PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "Marianne is a French horror web television series created and directed by <PERSON>, written by <PERSON><PERSON> and <PERSON><PERSON><PERSON> and starring <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. The plot revolves around the young novelist <PERSON> who realizes that the characters she writes in her horror novels are also in the real world. The series was released on 13 September 2019 on Netflix. The series was canceled after one season in January 2020.", "summary_localized": {"fr": "Marianne est une série télévisée française créée et réalisée par <PERSON>, et écrite par <PERSON> et <PERSON><PERSON><PERSON>, mise en ligne le 13 septembre 2019 sur Netflix. Les rôles principaux sont interprétés par Vic<PERSON> Bois, <PERSON><PERSON> et <PERSON><PERSON><PERSON><PERSON>. La série raconte l'histoire de la jeune écrivaine Emma, qui réalise que les personnages qu'elle a inventés dans ses romans d'horreur existent dans le monde réel."}, "type": {"primary": "TV series", "secondary": "horror"}, "type_localized": {"fr": {"primary": "Série télévisée", "secondary": "<PERSON><PERSON><PERSON>"}}, "updated_at": {"__time__": "2020-11-01T23:00:00.000Z"}, "urls": {"affiliate": "https://www.netflix.com/watch/80217779", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fmarianne.jpg?alt=media&token=575ad499-613e-4d31-8ada-abf64750c966", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_(TV_series)", "youtube": ""}, "__collections__": {}}, "4WbSyZ1Gz8V7gx0mBrZH": {"created_at": {"__time__": "2020-06-26T11:58:52.736Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Développeuse Du Dimanche", "release": {"before_common_era": false, "original": null}, "summary": "Développeuse Du Dimanche is a YouTube channel created by <PERSON>, a french game designer. The channel talks about the process to make a game and analyze game concepts.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Video Games"}, "type_localized": {}, "updated_at": {"__time__": "2020-06-26T11:58:52.736Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLola_Guilldou.jpg?alt=media&token=dc99d468-45bf-4811-9a90-a738b51236a0", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.lola-guilldou.com/", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "4cXFO1e9GPUEBkrIb0Kd": {"created_at": {"__time__": "2020-09-22T11:52:02.363Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "The Man Who Laughs", "release": {"before_common_era": false, "original": null}, "summary": "The Man Who Laughs is a novel by <PERSON>, originally published in April 1869 under the French title L'Homme qui rit. It takes place in England in the 1680s and 1700s, during the reigns of <PERSON> and <PERSON>, respectively, and depicts England's royalty and aristocracy of the time as cruel and power-hungry.", "summary_localized": {}, "type": {"primary": "Book", "secondary": "Novel"}, "type_localized": {}, "updated_at": {"__time__": "2020-09-22T11:52:02.363Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMan_Who_Laughs_1869.jpg?alt=media&token=1de1a823-6eb8-4731-97c6-67d95c289a59", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Man_Who_Laughs", "youtube": ""}, "__collections__": {}}, "4zXzqAfoX6ygEpBzXK1I": {"created_at": {"__time__": "2020-02-01T22:11:00.338Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "Mindhunter", "release": {"before_common_era": false, "original": null}, "summary": "Mindhunter is an American crime thriller television series created by <PERSON>, based on the true-crime book <PERSON><PERSON><PERSON>: Inside the FBI's Elite Serial Crime Unit written by <PERSON> and <PERSON>. Mindhunter revolves around FBI agents <PERSON> (<PERSON>) and <PERSON> (<PERSON>), along with psychologist <PERSON> (<PERSON>), who operate the FBI's Behavioral Science Unit within the Training Division at the FBI Academy in Quantico, Virginia. They interview imprisoned serial killers to understand how they think, with the hope of applying this knowledge to solve ongoing cases.", "summary_localized": {"fr": "Mindhunter est une série télévisée américaine créée par <PERSON>, produite par <PERSON> et <PERSON><PERSON><PERSON>, qui est diffusée sur Netflix à partir du 13 octobre 2017. Elle s'inspire des livres Mindhunter : Dans la tête d’un profileur et Le tueur en face de moi (parution française en novembre 2019) écrits par <PERSON> et <PERSON>, éditions Michel <PERSON>. Avant même la diffusion de sa première saison, la série a été renouvelée pour une seconde saison."}, "type": {"primary": "TV series", "secondary": "Drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.338Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/commons/thumb/3/30/Mindhunter_Logo.svg/1280px-Mindhunter_Logo.svg.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Mindhunter_(TV_series)", "youtube": ""}, "__collections__": {}}, "5LSgjd8IacM5DRJqNShz": {"created_at": {"__time__": "2020-02-01T22:11:00.338Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Nowtech", "release": {"before_common_era": false, "original": null}, "summary": "Nowtech is a french content production about technology and applications. They produce a daily live on YouTube.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Technology"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.338Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AGF-l7-9Hl942WiXIE222b_roDy9sqATUNDrtByE-Q=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/user/NowTechTVfr", "wikipedia": "https://nowtech.tv/", "youtube": ""}, "__collections__": {}}, "5RAtSCGzoS4EewMsS13Y": {"created_at": {"__time__": "2020-02-01T22:11:00.338Z"}, "image": {"credits": {"artist": "Dirty Biology", "beforeJC": false, "company": "YouTube", "date": 1518562800000, "location": "", "name": "À quoi ça sert d'être triste ? - YouAsk #2", "url": "https://i.ytimg.com/vi/RFI1CzUoeLk/maxresdefault.jpg"}}, "language": "fr", "links": [], "name": "À quoi ça sert d'être triste ? - YouAsk #2", "release": {"before_common_era": false, "original": {"__time__": "2018-02-13T23:00:00.000Z"}}, "summary": "A french video about the utility of sadness. The subject is studied and presented by <PERSON> from the channel Dirty Biology.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Sciences"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.338Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FÀ quoi ça sert d'être triste ? - YouAsk #2-1616029238900.jpg?alt=media", "image_name": "À quoi ça sert d'être triste ? - YouAsk #2-1616029238900.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/RFI1CzUoeLk?t=370https://youtu.be/RFI1CzUoeLk?t=370", "youtube": "https://www.youtube.com/watch?v=RFI1CzUoeLk"}, "__collections__": {}}, "65crnCsKUcaEEjxXXpDU": {"created_at": {"__time__": "2020-03-23T00:32:12.823Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "SoCraTes", "release": {"before_common_era": false, "original": null}, "summary": "SoCraTes UK is a non-profit, international Software Crafters retreat for open-minded crafters who want to improve their craft and the software industry as a whole. SoCraTes UK is totally community-focused. It's a great opportunity to speak to and code with many like-minded and talented developers in a very relaxed and beautiful setting.", "summary_localized": {}, "type": {"primary": "Conference", "secondary": "Sciences"}, "type_localized": {}, "updated_at": {"__time__": "2020-03-23T00:32:12.824Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://socratesuk.org/img/SoCraTes_UK_circle.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://socratesuk.org/", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "6S6jxQyUaFehs0yiqtq5": {"created_at": {"__time__": "2024-01-19T18:26:19.413Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-01T17:02:48.815Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Bérénice", "release": {"before_common_era": false, "original": {"__time__": "1670-07-31T22:00:00.000Z"}}, "summary": "Berenice is a five-act tragedy by the French 17th-century playwright <PERSON>. Berenice was not played often between the 17th and the 20th centuries. It was premiered on 21 November 1670 by the Comédiens du Roi at the Hôtel de Bourgogne.", "type": {"primary": "play", "secondary": "<PERSON><PERSON><PERSON><PERSON>"}, "updated_at": {"__time__": "2024-01-19T18:26:19.413Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBérénice-1705688781306.jpg?alt=media", "imageName": "Bérénice-1705688781306.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/B%C3%A9r%C3%A9nice", "youtube": ""}, "__collections__": {}}, "6j6mk58xtcmANWo7FAQI": {"created_at": {"__time__": "2020-04-22T22:11:03.925Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Unorthodox (miniseries)", "release": {"before_common_era": false, "original": null}, "summary": "Unorthodox is a German-American drama miniseries that debuted on Netflix on March 26, 2020. The series is loosely based on <PERSON>'s 2012 autobiography Unorthodox: The Scandalous Rejection of My Hasidic Roots. It is the first Netflix series to be primarily in Yiddish. ", "summary_localized": {}, "type": {"primary": "TV Series", "secondary": "Drama, Miniseries"}, "type_localized": {}, "updated_at": {"__time__": "2020-04-22T22:11:03.926Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FUnorthodox%20(miniseries).jpg?alt=media&token=1d2e8193-6304-442e-9081-3860bd1bd241", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Unorthodox_(miniseries)", "youtube": ""}, "__collections__": {}}, "71NDYOTWE3SinPk9vfOA": {"created_at": {"__time__": "2020-12-23T12:12:56.391Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "A Poison Tree", "release": {"before_common_era": false, "original": {"__time__": "1783-12-31T23:50:39.000Z"}}, "summary": "\"A Poison Tree\" is a poem written by <PERSON>, published in 1794 as part of his Songs of Experience collection. It describes the narrator's repressed feelings of anger towards an individual, emotions which eventually lead to murder.", "summary_localized": {}, "type": {"primary": "Poem", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-12-23T12:12:56.391Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FA Poison Tree-1608725578743.jpg?alt=media", "image_name": "A Poison Tree-1608725578743.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/A_Poison_Tree", "youtube": ""}, "__collections__": {}}, "7MHZcYTNerPfEPKIlWmO": {"created_at": {"__time__": "2020-11-04T21:53:54.767Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Ratched", "release": {"before_common_era": false, "original": {"__time__": "2020-07-17T22:00:00.000Z"}}, "summary": "Ratched is an American psychological thriller streaming television series about the character of the same name from <PERSON>'s 1962 novel One Flew Over the Cuckoo's Nest. Created by <PERSON> and developed by <PERSON>, the series stars <PERSON> in the title role and serves as a prequel to the novel.\n\nThe plot is a young nurse at a mental institution becomes jaded and bitter before turning into a full-fledged monster to her patients.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Psychological thriller"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-04T21:53:54.767Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRatched-1604526838388.jpg?alt=media", "image_name": "Ratched-1604526838388.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80213445", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Ratched_(TV_series)", "youtube": ""}, "__collections__": {}}, "7es6aUpgiEdsN5JuC4xz": {"created_at": {"__time__": "2020-11-05T12:59:13.811Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Modiiie", "release": {"before_common_era": false, "original": {"__time__": "2012-12-31T23:00:00.000Z"}}, "summary": "<PERSON><PERSON><PERSON><PERSON> is a french Twitch streamer who has a channel talking about social sciences, psychology and video games. She also works as a teacher in political science. She's very active in multiple fields like social documentaries on YouTube, a personal blog, and live reading.", "summary_localized": {}, "type": {"primary": "Twitch", "secondary": "Social/Psychology/Video games"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-05T12:59:13.811Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/Modiie/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FModiiie-1604581157013.png?alt=media", "image_name": "Modiiie-1604581157013.png", "imdb": "", "instagram": "https://www.instagram.com/m0diie/", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/modiiie", "twitter": "https://twitter.com/m0diie", "website": "https://carnetmodiie.com/", "wikipedia": "", "youtube": "https://www.youtube.com/c/modiie"}, "__collections__": {}}, "7fYvbHmYqcbgruGCdJo5": {"created_at": {"__time__": "2020-04-08T06:59:27.694Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON> (TV series)", "release": {"before_common_era": false, "original": null}, "summary": "Freud is an Austrian-German crime television series re-imagining the life of a young <PERSON><PERSON><PERSON>. 8 episodes have been produced, with the first series airing on 23 March 2020 on Netflix. While investigating several disappearances and murders in Vienna, young <PERSON><PERSON><PERSON> and a psychic medium become entangled in an occult conspiracy.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Crime"}, "type_localized": {}, "updated_at": {"__time__": "2020-04-08T06:59:27.694Z"}, "urls": {"affiliate": "https://www.netflix.com/watch/80209184", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/4/4c/Freud_Netflix.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Freud_(TV_series)", "youtube": ""}, "__collections__": {}}, "7naEZTo8VCBpjwg9wt9o": {"created_at": {"__time__": "2023-12-12T23:58:36.022Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-09T14:17:45.522Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "1966-08-08T23:00:00.000Z"}}, "summary": "«Entretiens» is a book based on a serie of audio recordings between <PERSON> and <PERSON> on a 8-days period. In the 30 hours of conversation, <PERSON> and <PERSON><PERSON><PERSON> discussed the whole work of the film director. The book is probably one of the most famous reference in cinema.", "type": {"primary": "book", "secondary": "Interview"}, "updated_at": {"__time__": "2023-12-12T23:58:36.022Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FEntretiens-1702425521373.jpg?alt=media", "imageName": "Entretiens-1702425521373.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "7qONqhv4Ev5NVQPwvgxX": {"created_at": {"__time__": "2023-09-20T15:30:31.843Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-09T14:28:19.848Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON> (play)", "release": {"before_common_era": false, "original": {"__time__": "1924-08-08T23:00:00.000Z"}}, "summary": "<PERSON> Joan is a play by <PERSON> about 15th-century French military figure <PERSON>. Premiering in 1923, three years after her canonization by the Roman Catholic Church, the play reflects <PERSON>'s belief that the people involved in <PERSON>'s trial acted according to what they thought was right. He wrote in his preface to the play:\n\n❝ There are no villains in the piece. Crime, like disease, is not interesting: it is something to be done away with by general consent, and that is all [there is] about it. It is what men do at their best, with good intentions, and what normal men and women find that they must and will do in spite of their intentions, that really concern us. ❞\n\n<PERSON> characterised <PERSON> as \"A Chronicle Play in 6 Scenes and an Epilogue \". <PERSON>, a simple peasant girl, claims to experience visions of <PERSON>, <PERSON>, and the archangel <PERSON>, which she says were sent by <PERSON> to guide her conduct.", "type": {"primary": "play", "secondary": "Drama"}, "updated_at": {"__time__": "2023-09-20T15:30:31.843Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSaint <PERSON> (play)-1695223838563.gif?alt=media", "imageName": "<PERSON> (play)-1695223838563.gif", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Saint%20Joan%20(play)", "youtube": ""}, "__collections__": {}}, "8Jm6JphPNqkKoqdC2gcS": {"created_at": {"__time__": "2020-11-03T16:48:46.796Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Achieve Your Goals with <PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2013-12-31T23:00:00.000Z"}}, "summary": "Achieve Your Goals with <PERSON> is a weekly podcast dedicated to empowering and equipping you with practical advice and strategies to achieve your goals and dreams. ", "summary_localized": {}, "type": {"primary": "Podcast", "secondary": "Productivity"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-03T16:48:46.796Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAchieve Your Goals with <PERSON>-1604422130539.jpg?alt=media", "image_name": "Achieve Your Goals with <PERSON>-1604422130539.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://halelrod.com/podcast/", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "8VoP8rZx3djm0Vf2t1VZ": {"created_at": {"__time__": "2024-06-05T13:19:08.513Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-01T15:33:20.305999Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Can art be popular?", "release": {"before_common_era": false, "original": {"__time__": "1924-07-31T23:00:00.000Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "updated_at": {"__time__": "2024-06-05T13:19:08.514Z"}, "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "8WaehxyK26OMj0mptjY8": {"created_at": {"__time__": "2020-02-01T22:11:00.338Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "Twilight of the Idols", "release": {"before_common_era": false, "original": null}, "summary": "Twilight of the Idols, or, How to Philosophize with a <PERSON> is a book by <PERSON>, written in 1888, and published in 1889.", "summary_localized": {"fr": "Crépuscule des idoles ou Comment on philosophe avec un marteau est une œuvre du philosophe Friedrich Nietzsche écrite et publiée en 1888 et conçue comme un résumé de sa philosophie. Le titre est une référence ironique au Crépuscule des dieux de <PERSON>. Elle est composée d'un avant-propos, de dix chapitres et d'un extrait d'Ainsi parlai<PERSON> (« Le marteau parle »)."}, "type": {"primary": "Book", "secondary": "Philosophy"}, "type_localized": {"fr": {"primary": "Livre", "secondary": "Philosophie"}}, "updated_at": {"__time__": "2020-02-01T22:11:00.338Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/commons/a/a6/Gotzen-dammerung.gif?1580587376781", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Twilight_of_the_Idols", "youtube": ""}, "__collections__": {}}, "8eXYpPCUd4oGojrTBRHr": {"created_at": {"__time__": "2024-07-22T19:41:59.549Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T19:39:47.750Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The X-Files", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T19:39:47.750Z"}}, "summary": "The X-Files is an American science fiction drama television series created by <PERSON>. The original television series aired from September 1993 to May 2002 on Fox. During its original run, the program spanned nine seasons, with 202 episodes. A short tenth season consisting of six episodes ran from January to February 2016. Following the ratings success of this revival, The X-Files returned for an eleventh season of ten episodes, which ran from January to March 2018. In addition to the television series, two feature films have been released: The 1998 film The X-Files and the stand-alone film The X-Files: I Want to Believe, released in 2008, six years after the original television run had ended.", "type": {"primary": "tv_series", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T19:41:59.549Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-X-Files-1721677320790.jpg?alt=media", "imageName": "The-X-Files-1721677320790.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_X-Files", "youtube": ""}, "__collections__": {}}, "8kTyErumdRiVLhPq5gdj": {"created_at": {"__time__": "2020-02-01T22:11:00.338Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Le Bourgeois gentilhomme", "release": {"before_common_era": false, "original": null}, "summary": "Le Bourgeois gentilhomme is a five-act comédie-ballet — a play intermingled with music, dance and singing — written by <PERSON><PERSON><PERSON>, first presented on 14 October 1670 before the court of <PERSON> XIV at the Château of Chambord by <PERSON><PERSON><PERSON>'s troupe of actors.", "summary_localized": {}, "type": {"primary": "Play", "secondary": "Comédie Ballet"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.338Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/commons/a/ac/Amphitryon.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Le_Bourgeois_gentilhomme", "youtube": ""}, "__collections__": {}}, "8u0M0hW5zj4ZXoE9913R": {"created_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": ["wi8c2dNdlyI6xzKmSSnQ"], "name": "The 4-Hour Workweek", "release": {"before_common_era": false, "original": null}, "summary": "The 4-Hour Workweek: Escape 9–5, Live Anywhere, and Join the New Rich is a self-help book by <PERSON>, an American writer, educational activist, and entrepreneur.", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/c/c3/The_4-Hour_Workweek_%28front_cover%29.jpg?1580597852118", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.amazon.fr/dp/0091929113/ref=cm_sw_em_r_mt_dp_U_IUllDbNADBNXT", "wikipedia": "https://www.amazon.fr/dp/0091929113/ref=cm_sw_em_r_mt_dp_U_IUllDbNADBNXT", "youtube": ""}, "__collections__": {}}, "8zzTbeSEZbcbAJaNzwsO": {"created_at": {"__time__": "2020-06-27T23:00:08.426Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "WIRED by Design: A Game Designer Explains the Counterintuitive Secret to Fun", "release": {"before_common_era": false, "original": null}, "summary": "<PERSON> at WIRED by Design, 2014.", "summary_localized": {}, "type": {"primary": "Conference", "secondary": "Game design"}, "type_localized": {}, "updated_at": {"__time__": "2020-06-27T23:00:08.426Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWIRED_The_Design_of_Fun.png?alt=media&token=3db85464-6c21-4c93-b1eb-13ae65b10235", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "96B4SjU6YnRbpvFZ622G": {"created_at": {"__time__": "2024-01-02T11:50:24.408Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-01-01T18:07:28.358Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "All The Lights We Cannot See", "release": {"before_common_era": false, "original": {"__time__": "2023-11-01T23:00:00.000Z"}}, "summary": "All the Light We Cannot See is an American drama limited series directed by <PERSON> and developed by <PERSON> for Netflix. Based on <PERSON>'s Pulitzer Prize winning novel of the same name, it stars <PERSON>, <PERSON> and <PERSON>. \n\nThe four-part series follows the stories of a blind French girl named <PERSON><PERSON><PERSON><PERSON> and a German soldier named <PERSON>, whose paths cross in occupied France during World War II.", "type": {"primary": "tv_series", "secondary": "Drama"}, "updated_at": {"__time__": "2024-01-02T11:50:24.408Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAll The Lights We Cannot See-1704196230440.jpg?alt=media", "imageName": "All The Lights We Cannot See-1704196230440.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81083008", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/All_the_Light_We_Cannot_See_(miniseries)", "youtube": "https://www.youtube.com/watch?v=ePLIObDy_HI"}, "__collections__": {}}, "98I8yv9EWTHYyMGNOcIB": {"created_at": {"__time__": "2020-07-07T12:31:30.680Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "Warrior Nun is an American fantasy drama web television series created by <PERSON> based on the comic book character <PERSON> by <PERSON>.\n\nOriginally developed as a feature film adaptation, the idea was re-imagined as a television series for Netflix when the service had given the production a series order for a first season. Filming takes place in multiple locations in Spain. ", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Fantasy drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-07-07T12:31:30.680Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fwarrior-nun.jpg?alt=media&token=ff2d9c8d-40b3-477d-a40d-a4b155259cc5", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80242724", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Warrior_Nun_(TV_series)", "youtube": ""}, "__collections__": {}}, "99kFdFtBtohjhVXsv4EQ": {"created_at": {"__time__": "2023-12-13T00:42:01.824Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-12-13T00:35:04.732Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Ondine", "release": {"before_common_era": false, "original": {"__time__": "1939-05-03T23:00:00.000Z"}}, "summary": "Ondine is a play written in 1938 by French dramatist <PERSON>, based on the 1811 novella Undine by the German Romantic <PERSON> that tells the story of <PERSON> and <PERSON><PERSON>. <PERSON> is a knight-errant who has been sent off on a quest by his betrothed. In the forest he meets and falls in love with <PERSON><PERSON>, a water sprite who is attracted to the world of mortal man. The subsequent marriage of people from different worlds is, of course, folly.", "type": {"primary": "play", "secondary": "Drama"}, "updated_at": {"__time__": "2023-12-13T00:42:01.824Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOndine-1702428123219.jpg?alt=media", "imageName": "Ondine-1702428123219.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Ondine%20(play)", "youtube": ""}, "__collections__": {}}, "9OqBB4B1G9IkhtfLeE2x": {"created_at": {"__time__": "2024-01-22T09:24:33.884Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-01-15T01:27:04.230Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "A Murder at the End of the World", "release": {"before_common_era": false, "original": {"__time__": "2023-11-13T23:00:00.000Z"}}, "summary": "«A Murder at the End of the World» is an American psychological thriller drama television miniseries created by <PERSON><PERSON> and <PERSON><PERSON> for FX. It stars <PERSON> as an amateur detective who attempts to solve a murder at an isolated Arctic retreat in Iceland. The supporting cast includes <PERSON><PERSON> herself, <PERSON> and <PERSON>.\n\nThe series follows <PERSON><PERSON>, an amateur detective, who is invited, along with eight other guests, by a reclusive billionaire to participate in a retreat at an isolated Arctic compound in Iceland. When one of the other guests is found dead, <PERSON><PERSON> must use all of her skills to prove it was murder against a tide of competing interests and before the killer takes another life.", "type": {"primary": "tv_series", "secondary": "Psychological thriller, Murder mystery"}, "updated_at": {"__time__": "2024-01-22T09:24:33.885Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FA Murder at the End of the World-1705915480068.jpg?alt=media", "imageName": "A Murder at the End of the World-1705915480068.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/A_Murder_at_the_End_of_the_World", "youtube": ""}, "__collections__": {}}, "9X3G0C51DqralCCIHU4d": {"created_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Amphitryon", "release": {"before_common_era": false, "original": null}, "summary": "Amphitryon is a French language comedy in a prologue and 3 Acts by <PERSON><PERSON><PERSON> which is based on the story of the Greek mythological character <PERSON><PERSON><PERSON><PERSON> as told by <PERSON><PERSON><PERSON> in his play from ca. 190-185 B.C. The play was first performed at the Théâtre du Palais-Royal in Paris on 13 January 1668.", "summary_localized": {}, "type": {"primary": "Play", "secondary": "Language comedy"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/commons/a/ac/Amphitryon.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Amphitryon_(Moli%C3%A8re_play)", "youtube": ""}, "__collections__": {}}, "9s8B3lsj7L3Pz649pEhA": {"created_at": {"__time__": "2023-12-13T00:28:05.710Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-09T13:45:59.536Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Un passage in Entracte", "release": {"before_common_era": false, "original": {"__time__": "1928-05-12T23:00:00.000Z"}}, "summary": "This is a reference cited in «La Dramaturgie» by <PERSON><PERSON><PERSON>, but we couldn't find more information beside the title and the publication date, for the moment.", "type": {"primary": "", "secondary": ""}, "updated_at": {"__time__": "2023-12-13T00:28:05.710Z"}, "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "9vKNoSH1CAiayseVfUva": {"created_at": {"__time__": "2021-05-24T10:56:52.757Z"}, "id": "", "image": {"credits": {"artist": "", "beforeJC": false, "company": "Netflix", "date": {"__time__": "2021-05-13T22:00:00.000Z"}, "location": "", "name": " The Woman in the Window.'s poster ", "url": "https://m.media-amazon.com/images/M/MV5BYmVlNWJkZWYtYmJkMy00YTZlLTgwODMtZjVmOGMzMmM1ZTk2XkEyXkFqcGdeQXVyMTAyMjQ3NzQ1._V1_UY1200_CR90,0,630,1200_AL_.jpg"}}, "language": "en", "name": "The Woman in the Window", "release": {"before_common_era": false, "original": {"__time__": "2021-05-13T22:00:00.000Z"}}, "summary": "Agoraphobic Dr. <PERSON> witnesses something she shouldn't while keeping tabs on the <PERSON> family, the seemingly picture perfect clan that lives across the way.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Mystery/Drama"}, "type_localized": {}, "updated_at": {"__time__": "2021-05-24T10:56:52.757Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Woman in the Window-1621853815774.jpg?alt=media", "image_name": "The Woman in the Window-1621853815774.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81092222", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Woman_in_the_Window_(2021_film)", "youtube": "https://youtu.be/v_0GJg_Jnlo"}, "__collections__": {}}, "9wvLO1AZM9iDbYjAxiYL": {"created_at": {"__time__": "2020-09-22T11:58:49.591Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Animal Liberation", "release": {"before_common_era": false, "original": null}, "summary": "Animal Liberation: A New Ethics for Our Treatment of Animals is a 1975 book by Australian philosopher <PERSON>. It is widely considered within the animal liberation movement to be the founding philosophical statement of its ideas. ", "summary_localized": {}, "type": {"primary": "Book", "secondary": "Philosophy"}, "type_localized": {}, "updated_at": {"__time__": "2020-09-22T11:58:49.591Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAnimal_Liberation_1975.jpg?alt=media&token=5f85e1fa-5c79-46d8-9a82-e60d4c11fa86", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Animal_Liberation_(book)", "youtube": ""}, "__collections__": {}}, "9xFKzT1DfQRZnd7R12ci": {"created_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Paum_é_e_s", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://soundcloud.com/paumees", "youtube": ""}, "__collections__": {}}, "A38G5H1lLkkxGNrzhaeg": {"created_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "<PERSON> Me Her", "release": {"before_common_era": false, "original": null}, "summary": "You Me Her is an American–Canadian comedy-drama television series that revolves around a suburban married coup\nle who are entering a three-way romantic relationship, otherwise known as a polyamorous relationship. The series is set in Portland, Oregon and was created by <PERSON>.", "summary_localized": {}, "type": {"primary": "TV Series", "secondary": "Comedie, Romance, Drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FYou%20Me%20Her.jpg?alt=media&token=cc786ede-9afa-407a-92e5-9dd713b429bc", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/ru/title/80103417", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/You_Me_Her", "youtube": ""}, "__collections__": {}}, "A6kOfQMRROZsH2Vdct5C": {"created_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Usbek&Rica", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://usbeketrica.com", "youtube": ""}, "__collections__": {}}, "ADj08wLPKvfr3F7UIEA0": {"created_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "The Misanthrope", "release": {"before_common_era": false, "original": null}, "summary": "The Misanthrope, or the Cantankerous Lover is a 17th-century comedy of manners in verse written by <PERSON><PERSON><PERSON>. It was first performed on 4 June 1666 at the Théâtre du Palais-Royal, Paris by the King's Players.", "summary_localized": {}, "type": {"primary": "Play", "secondary": "Comedy of manners"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/commons/0/08/LeMisanthrope.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Misanthrope", "youtube": ""}, "__collections__": {}}, "AHXJciwyppF85UV20ZIT": {"created_at": {"__time__": "2023-09-22T00:35:39.630Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "1970-01-01T00:00:00.000Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Expanse", "release": {"before_common_era": false, "original": {"__time__": "2015-12-14T00:00:00.692Z"}}, "summary": "The Expanse is an American science fiction television series developed by <PERSON> and <PERSON> for the Syfy network, and is based on the series of novels of the same name by <PERSON>. \n\nThe series is set in a future where humanity has colonized the Solar System. It follows a disparate band of protagonists—United Nations Security Council member <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>), police detective <PERSON><PERSON> (<PERSON>), ship's officer <PERSON> (<PERSON>) and his crew—as they unwittingly unravel and place themselves at the center of a conspiracy that threatens the system's fragile state of cold war, while dealing with existential crises brought forth by newly discovered alien technology.", "type": {"primary": "tv_series", "secondary": "Science-Fiction"}, "updated_at": {"__time__": "2023-09-22T00:35:39.630Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Expanse-1695342946385.jpg?alt=media", "imageName": "The Expanse-1695342946385.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Expanse_(TV_series)", "youtube": ""}, "__collections__": {}}, "B4IeiKgepP5TpDE2nBjn": {"created_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Pourquoi l'humanité a-t-elle failli disparaître ? - DBY #33", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/wZyx5EEfUSE?t=185", "youtube": ""}, "__collections__": {}}, "BLPMtoGB7NtYP7tEkAnf": {"created_at": {"__time__": "2020-11-05T12:41:49.873Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "ZLAN", "release": {"before_common_era": false, "original": {"__time__": "2019-05-10T22:00:00.000Z"}}, "summary": "ZLAN is a LAN video games competition organized by ZeratoR and ZQSD Productions. This competition has been shaped for live streaming and a lot of streamers take part in the competition. The event occur in a weekend with almost 200 players competing in duos on 10 selected games. There're always unexpected games like mental math or dictation. The four first teams share a 50k cash-prize.", "summary_localized": {}, "type": {"primary": "Event", "secondary": "Video games"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-05T12:41:49.873Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FZLAN-1604580113107.jpg?alt=media", "image_name": "ZLAN-1604580113107.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/zlan_fr", "website": "http://z-lan.fr/", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "BXr4qiBpMB3jdcgfOTmL": {"created_at": {"__time__": "2020-02-01T22:11:00.339Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Marketing Mania", "release": {"before_common_era": false, "original": null}, "summary": "Marketing Mania is a multi-production content about marketing and psychology. The content is mainly available through the official website, YouTube, a podcast and a book.", "summary_localized": {"fr": "Marketing Mania est une production de contenus analysant le marketing et la psychologie. Le contenu se compose principalement du site officiel, de vidéos YouTube, d'un podcast et d'un livre."}, "type": {"primary": "Content production", "secondary": "Marketing"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-02T22:11:00.000Z"}, "urls": {"affiliate": "https://www.youtube.com/channel/UCSmUdD2Dd_v5uqBuRwtEZug", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMarketing%20Mania.jpg?alt=media&token=822c5c4f-3af8-4c4a-be81-1ffe82b4a884", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://marketingmania.fr", "wikipedia": "", "youtube": "https://www.youtube.com/channel/UCSmUdD2Dd_v5uqBuRwtEZug"}, "__collections__": {}}, "BmQXtgwzgiHqxIfy1wZl": {"created_at": {"__time__": "2020-04-23T23:08:58.491Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Great Gatsby (2013 film)", "release": {"before_common_era": false, "original": null}, "summary": "The Great Gatsby is a 2013 romantic drama film based on <PERSON><PERSON>'s 1925 novel of the same name. The film was co-written and directed by <PERSON><PERSON> and stars <PERSON> as the eponymous <PERSON>, with <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Romantic, Drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-04-23T23:08:58.491Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/c/c2/TheGreatGatsby2013Poster.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Great_Gatsby_(2013_film)", "youtube": ""}, "__collections__": {}}, "CHx50h3n6L9WyBUddob0": {"created_at": {"__time__": "2024-07-22T14:20:40.922Z"}, "id": "CHx50h3n6L9WyBUddob0", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T14:16:32.210Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Lion King", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T14:16:32.210Z"}}, "summary": "In the Pride Lands of Tanzania, a pride of lions rule over the kingdom from Pride Rock. King <PERSON><PERSON><PERSON> and Queen <PERSON><PERSON>'s newborn son, <PERSON><PERSON>, is presented to the gathering animals by <PERSON><PERSON><PERSON> the mandrill, the kingdom's shaman and advisor. <PERSON><PERSON><PERSON>'s younger brother, <PERSON><PERSON>, covets the throne.\nAfter <PERSON><PERSON> grows into a cub, <PERSON><PERSON><PERSON> shows him the Pride Lands and explains the responsibilities of kingship and the \"circle of life,\" which connects all living things. One day, <PERSON><PERSON> and his best friend <PERSON><PERSON> explore an elephant graveyard, where the two are chased by three spotted hyenas named <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON><PERSON> is alerted by his majordomo, the hornbill <PERSON><PERSON><PERSON>, and rescues the cubs. Though disappointed with <PERSON><PERSON> for disobeying him and endangering himself and <PERSON><PERSON>, <PERSON><PERSON><PERSON> forgives him and explains that the great kings of the past watch over them from the night sky, from which he will one day watch over <PERSON><PERSON>. <PERSON><PERSON>, having planned the attack, visits the hyenas and convinces them to help him kill both <PERSON><PERSON><PERSON> and <PERSON><PERSON> in exchange for hunting rights in the Pride Lands.", "type": {"primary": "film", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T14:20:40.922Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-Lion-King-1721675125871.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Lion_King", "youtube": ""}, "__collections__": {}}, "CJiRDGrZvyCeiFvp5StI": {"created_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "What Can You Learn from Your Competition?: Crash Course Business Entrepreneurship #4", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/5hL66Xl6W6M", "youtube": ""}, "__collections__": {}}, "CSeyguhah3vFhEgYIO9q": {"created_at": {"__time__": "2020-09-22T11:30:14.888Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "<PERSON> Rire Jau<PERSON> (duo)", "release": {"before_common_era": false, "original": null}, "summary": "<PERSON> Rire <PERSON> is two french YouTubers, <PERSON> and <PERSON>, which make humorous sketches videos on YouTube.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Sketches"}, "type_localized": {}, "updated_at": {"__time__": "2020-09-22T11:30:14.888Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/LeRireJauneOfficiel/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe_Rire_Jaune.jpg?alt=media&token=aef2303c-4ae7-417e-bb5a-e34661e1b727", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/henrirejaune", "twitter": "https://twitter.com/SuperKevinTran", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(duo)", "youtube": "https://www.youtube.com/user/LeRiiiiiiiireJaune"}, "__collections__": {}}, "CvTFhuKKeNVcVhzkYU4x": {"created_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "La Terre pourrait-elle être un DONUT ? - DBY #50", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/eZU9qsugcBY", "youtube": ""}, "__collections__": {}}, "CxqzswVrf2whaGtZOeTE": {"created_at": {"__time__": "2023-12-09T14:00:29.451Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-09T14:25:44.515Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Le petit prince", "release": {"before_common_era": false, "original": {"__time__": "1943-03-31T22:00:00.000Z"}}, "summary": "The Little Prince (French: <PERSON> Petit <PERSON>, pronounced [lə p(ə)ti pʁɛ̃s]) is a novella written and illustrated by French aristocrat, writer, and military pilot <PERSON>. \n\nIt was first published in English and French in the United States by Reynal & Hitchcock in April 1943 and was published posthumously in France following liberation; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s works had been banned by the Vichy Regime. \n\nThe story follows a young prince who visits various planets, including Earth, and addresses themes of loneliness, friendship, love, and loss. Despite its style as a children's book, The Little Prince makes observations about life, adults, and human nature.", "type": {"primary": "book", "secondary": "Novel"}, "updated_at": {"__time__": "2023-12-09T14:00:29.451Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe petit prince-1702130435864.jpg?alt=media", "imageName": "Le petit prince-1702130435864.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The%20Little%20Prince", "youtube": ""}, "__collections__": {}}, "Cz4cpI8QwSUUoecQPuqP": {"created_at": {"__time__": "2021-02-24T23:17:48.928Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Passengers", "release": {"before_common_era": false, "original": {"__time__": "2016-12-21T23:00:00.000Z"}}, "summary": "Passengers is a 2016 American science fiction romance film directed by <PERSON><PERSON> and written by <PERSON>, partially based on the 1950s EC Comics story '50 Girls 50'. The plot os the film is: During a voyage to a distant colony planet, <PERSON>'s hypersleep pod malfunctions, waking him up.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Science fiction romance"}, "type_localized": {}, "updated_at": {"__time__": "2021-02-24T23:17:48.928Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPassengers-1614208672500.jpg?alt=media", "image_name": "Passengers-1614208672500.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80117456", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.50b53a56-01d6-bd25-1c5f-98f6cd3da59b", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Passengers_(2016_film)", "youtube": "https://www.youtube.com/watch?v=T_NgkMq53yo"}, "__collections__": {}}, "DAqYjkGoZsVouAvMHzYh": {"created_at": {"__time__": "2021-01-24T19:06:42.112Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Cyberpunk 2077", "release": {"before_common_era": false, "original": {"__time__": "2020-12-09T23:00:00.000Z"}}, "summary": "Cyberpunk 2077 is a 2020 action role-playing video game developed and published by CD Projekt. The story takes place in Night City, an open world set in the Cyberpunk universe.", "summary_localized": {}, "type": {"primary": "Video game", "secondary": "Action role playing game"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-24T19:06:42.112Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/CyberpunkGame", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyberpunk 2077-1611515204246.jpg?alt=media", "image_name": "Cyberpunk 2077-1611515204246.jpg", "imdb": "", "instagram": "https://www.instagram.com/CyberpunkGame/", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/CyberpunkGame", "website": "https://www.cyberpunk.net", "wikipedia": "https://en.wikipedia.org/wiki/Cyberpunk_2077", "youtube": "https://www.youtube.com/channel/UC4zyoIAzmdsgpDZQfO1-lSA"}, "__collections__": {}}, "DlVSm57XK5OKZJCaPmO4": {"created_at": {"__time__": "2020-12-23T12:13:05.513Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Auguries of Innocence", "release": {"before_common_era": false, "original": {"__time__": "1862-12-31T23:50:39.000Z"}}, "summary": "\"Auguries of Innocence\" is a poem by <PERSON>, from a notebook of his now known as the Pickering Manuscript.[1] It is assumed to have been written in 1803, but was not published until 1863 in the companion volume to <PERSON>'s biography of <PERSON>.", "summary_localized": {}, "type": {"primary": "Poem", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-12-23T12:13:05.513Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAuguries of Innocence-1608725587040.jpg?alt=media", "image_name": "Auguries of Innocence-1608725587040.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Auguries_of_Innocence", "youtube": ""}, "__collections__": {}}, "EDRwqgBONNg8cAaAhg8q": {"created_at": {"__time__": "2020-10-25T00:23:00.091Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "La Révolution", "release": {"before_common_era": false, "original": {"__time__": "2020-10-15T22:00:00.000Z"}}, "summary": "La Révolution is a 2020 French-language supernatural drama series produced for Netflix starring <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>. In a reimagined history of the French Revolution, <PERSON> discovers that a virus is spreading among the nobility causing them to murder commoners.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Supernatural drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-10-25T00:23:00.091Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa Révolution-1603585384125.jpg?alt=media", "image_name": "La Révolution-1603585384125.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80992775", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/La_R%C3%A9volution", "youtube": ""}, "__collections__": {}}, "EpMEKaGkQdHN6W0Ii6XY": {"created_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "Coding is a Privilege", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/PtKOzKNJF-s?t=977", "youtube": ""}, "__collections__": {}}, "F2Li6Usbb6EH4qVFU1zD": {"created_at": {"__time__": "2021-01-09T23:00:39.218Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Chilling Adventures of Sabrina", "release": {"before_common_era": false, "original": {"__time__": "2018-10-25T22:00:00.000Z"}}, "summary": "This adaptation of the \"<PERSON> the Teenage Witch\" tale is a dark coming-of-age story that traffics in horror and the occult. In the reimagined origin story, <PERSON> wrestles to reconcile her dual nature ― half-witch, half-mortal ― while standing against the evil forces that threaten her, her family ― including aunts <PERSON> and <PERSON><PERSON><PERSON> and the daylight world humans inhabit. <PERSON><PERSON><PERSON> (\"Mad Men\") leads the cast in the titular role of the show that is based on a comic series of the same name.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Supernatural horror, Dark fantasy"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-09T23:00:39.218Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FChilling Adventures of Sabrina-1610233243606.jpg?alt=media", "image_name": "Chilling Adventures of Sabrina-1610233243606.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80223989", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Chilling_Adventures_of_<PERSON>_(TV_series)", "youtube": ""}, "__collections__": {}}, "F5K5HVw6X9sQwEXHRyK6": {"created_at": {"__time__": "2021-05-02T21:34:48.469Z"}, "id": "", "image": {"credits": {"artist": "", "beforeJC": false, "company": "", "date": {"__time__": "2021-03-20T14:26:23.249Z"}, "location": "", "name": "", "url": "https://lucioles-avignon.fr/initiative/les-zecolonomiks/"}}, "language": "fr", "name": "Les zécolonomiks", "release": {"before_common_era": false, "original": {"__time__": "2018-08-31T22:00:00.000Z"}}, "summary": "Les Z’écolonomiks is an online shop sellings goods with an ecology intention. They distribute durable, sustainable, vegan, and zero waste products like soaps, bags, or cosmetics.", "summary_localized": {}, "type": {"primary": "Online Shop", "secondary": "Goods"}, "type_localized": {}, "updated_at": {"__time__": "2021-05-02T21:34:48.469Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/LesZecolonomiks/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLes zécolonomiks-1619991291517.jpg?alt=media", "image_name": "Les zécolonomiks-1619991291517.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.les-zecolonomiks.com/", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "FVDMTEh1Y0zE2SS4xxlr": {"created_at": {"__time__": "2021-01-14T22:39:47.972Z"}, "image": {"credits": {"artist": "", "beforeJC": false, "company": "", "date": 1616029814920, "location": "", "name": "Constante Plank | Relativité restreinte | Photon", "url": "https://imgv2-2-f.scribdassets.com/img/document/*********/original/f08f1bb527/1612493345?v=1"}}, "language": "en", "name": "Letter to <PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "1941-12-31T22:00:00.000Z"}}, "summary": "<PERSON>'s letter to <PERSON> talking about relativity theory.", "summary_localized": {}, "type": {"primary": "Letter", "secondary": "Sciences"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-14T22:39:47.972Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLetter to <PERSON><PERSON>nc<PERSON>-1616030124745.jpg?alt=media", "image_name": "Letter to <PERSON><PERSON>-1616030124745.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>", "youtube": ""}, "__collections__": {}}, "FYW6ktebRknOyXOC9PP4": {"created_at": {"__time__": "2024-07-22T14:42:35.501Z"}, "id": "FYW6ktebRknOyXOC9PP4", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T14:39:54.035Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": " <PERSON> and the Goblet of Fire", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T14:39:54.035Z"}}, "summary": "<PERSON> and the Goblet of Fire is a 2005 fantasy film directed by <PERSON> from a screenplay by <PERSON>, based on the 2000 novel of the same name by <PERSON><PERSON><PERSON><PERSON>. It is the sequel to <PERSON> and the Prisoner of Azkaban (2004) and the fourth instalment in the <PERSON> film series. The film stars <PERSON> as <PERSON>, alongside <PERSON> and <PERSON> as <PERSON>'s best friends <PERSON> and <PERSON><PERSON><PERSON> respectively. The story follows <PERSON>'s fourth year at Hogwarts as he is chosen by the Goblet of Fire to compete in the Triwizard Tournament.", "type": {"primary": "film", "secondary": "Book"}, "updated_at": {"__time__": "2024-07-22T14:42:35.501Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F-<PERSON>-<PERSON>-and-the-Goblet-of-Fire-1721674880953.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>_and_the_Goblet_of_Fire_(film)", "youtube": ""}, "__collections__": {}}, "FZwuRHyUIeuY9I6HDB2J": {"created_at": {"__time__": "2020-09-19T18:40:12.182Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Notion", "release": {"before_common_era": false, "original": null}, "summary": "Notion is an application that provides components such as databases, kanban boards, wikis, calendars and reminders. Users can connect these components to create their own systems for knowledge management, note taking, data management, project management, among others.", "summary_localized": {}, "type": {"primary": "Application", "secondary": "Knowloedge management"}, "type_localized": {}, "updated_at": {"__time__": "2020-09-19T18:40:12.182Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FNotion_app_logo.png?alt=media&token=756b0e18-09c9-409d-bba1-c45cddfc7eef", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.notion.so/", "wikipedia": "https://en.wikipedia.org/wiki/Notion_(app)", "youtube": ""}, "__collections__": {}}, "Ff0OXZfUUzqtG9i9B95b": {"created_at": {"__time__": "2021-05-02T20:40:29.567Z"}, "id": "", "image": {"credits": {"artist": "", "beforeJC": false, "company": "Allocine", "date": {"__time__": "2001-05-09T22:00:00.000Z"}, "location": "", "name": "", "url": "https://www.allocine.fr/film/fichefilm-28682/dvd-blu-ray/?cproduct=85868"}}, "language": "en", "name": "Mulholland Drive", "release": {"before_common_era": false, "original": {"__time__": "2001-05-15T22:00:00.000Z"}}, "summary": "<PERSON>, a dark-haired amnesiac, and <PERSON>, a perky blonde actress, team up to find clues related to <PERSON>'s accident and ascertain her true identity.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Mystery, Thriller"}, "type_localized": {}, "updated_at": {"__time__": "2021-05-02T20:40:29.567Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMulholland Drive-1619988032653.jpg?alt=media", "image_name": "Mulholland Drive-1619988032653.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/60021646", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.f0b31af3-62f9-02cf-bde9-3fd2f12dd6a1?autoplay=1&ref_=atv_cf_strg_wb", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Mulholland_Drive_(film)", "youtube": "https://www.youtube.com/watch?v=epu30GulbT8"}, "__collections__": {}}, "Filpvrosx2WwrKFPHODR": {"created_at": {"__time__": "2020-11-05T13:11:01.763Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Dark", "release": {"before_common_era": false, "original": {"__time__": "2017-11-30T23:00:00.000Z"}}, "summary": "Dark is a German science fiction thriller streaming television series co-created by <PERSON><PERSON> and <PERSON><PERSON><PERSON>.[5][6][7] It ran for three seasons from 2017 to 2020.\n\nThe plot is: When two children go missing in a small German town, its sinful past is exposed along with the double lives and fractured relationships that exist among four families as they search for the kids.\n\nThe mystery-drama series introduces an intricate puzzle filled with twists that includes a web of curious characters, all of whom have a connection to the town's troubled history -- whether they know it or not. The story includes supernatural elements that tie back to the same town in 1986. \"Dark\" represents the first German original series produced for Netflix.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Sciences fiction thriller"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-05T13:11:01.763Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDark-1604581863605.jpg?alt=media", "image_name": "Dark-1604581863605.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/80100172", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Dark_(TV_series)", "youtube": ""}, "__collections__": {}}, "G6YEaTdoV8CGc0ujNFza": {"created_at": {"__time__": "2024-07-22T19:34:05.807Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T19:28:35.302Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Star Wars: Episode V – The Empire Strikes Back", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T19:28:35.302Z"}}, "summary": "The Empire Strikes Back (also known as Star Wars: Episode V – The Empire Strikes Back) is a 1980 American epic space opera film directed by <PERSON><PERSON> from a screenplay by <PERSON> and <PERSON>, based on a story by <PERSON>. The sequel to Star Wars (1977),[b] it is the second film in the Star Wars film series and the fifth chronological chapter of the \"Skywalker Saga\". Set three years after the events of Star Wars, the film recounts the battle between the malevolent Galactic Empire, led by the Emperor, and the Rebel Alliance, led by <PERSON> and Princess <PERSON>. As the Empire goes on the offensive, <PERSON> trains to master the Force so he can confront the Emperor's powerful disciple, <PERSON><PERSON>. The ensemble cast includes <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>.", "type": {"primary": "film", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T19:34:05.807Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStar-Wars--Episode-V---The-Empire-Strikes-Back-1721676852123.jpg?alt=media", "imageName": "Star-Wars--Episode-V---The-Empire-Strikes-Back-1721676852123.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Empire_Strikes_Back", "youtube": ""}, "__collections__": {}}, "GZiO4LLK3uW3glxGu5N7": {"created_at": {"__time__": "2021-01-24T19:16:14.733Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Dark Pictures Anthology: Little Hope", "release": {"before_common_era": false, "original": {"__time__": "2020-10-29T23:00:00.000Z"}}, "summary": "The Dark Pictures Anthology: Little Hope, also known simply as Little Hope, is an interactive drama survival horror video game developed by Supermassive Games and published by Bandai Namco Entertainment. ", "summary_localized": {}, "type": {"primary": "Video Game", "secondary": "Survival, Horror"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-24T19:16:14.733Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Dark Pictures Anthology: Little Hope-1611515777046.jpg?alt=media", "image_name": "The Dark Pictures Anthology: <PERSON>-1611515777046.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.thedarkpictures.com/little-hope", "wikipedia": "https://en.wikipedia.org/wiki/The_Dark_Pictures_Anthology:_<PERSON>_<PERSON>", "youtube": "https://www.youtube.com/channel/UCFPrg1ERpYha46QVHTF75HA"}, "__collections__": {}}, "GboI8yXhJf5jUsW83tCt": {"created_at": {"__time__": "2021-03-02T18:34:39.036Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Firefly Lane", "release": {"before_common_era": false, "original": {"__time__": "2021-02-02T23:00:00.000Z"}}, "summary": "Firefly Lane is an American drama streaming television series created by <PERSON> for Netflix. The series is based on the novel of the same name by <PERSON><PERSON>. The plot tells the story of <PERSON><PERSON> and <PERSON> who meet as young girls on Firefly Lane and become inseparable friends throughout 30 years of ups and downs.", "summary_localized": {}, "type": {"primary": "Streaming TV series", "secondary": "Drama"}, "type_localized": {}, "updated_at": {"__time__": "2021-03-02T18:34:39.036Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFirefly Lane-1614710083338.jpg?alt=media", "image_name": "Firefly Lane-1614710083338.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80994340", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Firefly_Lane", "youtube": ""}, "__collections__": {}}, "HT1BE1a9QV0OQifgT0ML": {"created_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Terre des hommes Terre des hommes", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Terre_des_hommes", "youtube": ""}, "__collections__": {}}, "HmSG89I0h6hMcXYLiNLx": {"created_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Nous n'irons pas dans l'espace - LetsPlayScience #3 (Part 3)", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/noADnHKyRmc?t=379", "youtube": ""}, "__collections__": {}}, "HppqCzS6w9oLRzfLEozU": {"created_at": {"__time__": "2021-05-24T11:12:10.232Z"}, "id": "", "image": {"credits": {"artist": "", "beforeJC": false, "company": "", "date": {"__time__": "2021-05-18T23:37:11.422Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Castlevania", "release": {"before_common_era": false, "original": {"__time__": "2017-07-06T22:00:00.000Z"}}, "summary": "Inspired by the popular video game series, this anime series is a dark medieval fantasy. It follows the last surviving member of the disgraced Belmont clan, <PERSON>, trying to save Eastern Europe from extinction at the hands of <PERSON>. As <PERSON> and his legion of vampires prepare to rid the world of humanity's stain, <PERSON> is no longer alone, and he and his misfit comrades race to find a way to save mankind from the grief-maddened <PERSON>.", "summary_localized": {}, "type": {"primary": "Animated series", "secondary": "Fantasy"}, "type_localized": {}, "updated_at": {"__time__": "2021-05-24T11:12:10.232Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCastlevania-1621854733462.jpg?alt=media", "image_name": "Castlevania-1621854733462.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/80095241", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Castlevania_(TV_series)", "youtube": ""}, "__collections__": {}}, "I49VoCY5gjuP2sxVSGZu": {"created_at": {"__time__": "2020-05-13T16:47:13.470Z"}, "image": {"credits": {"artist": "", "beforeJC": false, "company": "Poncecorp", "date": 1615711595291, "location": "", "name": "<PERSON>'s Twitch profile picture", "url": "https://static-cdn.jtvnw.net/jtv_user_pictures/125bbc49-45d1-432c-92b3-f5aef1e7ab21-profile_image-600x600.png"}}, "language": "fr", "name": "Ponce (Twitch)", "release": {"before_common_era": false, "original": {"__time__": "2021-03-14T08:46:35.291Z"}}, "summary": "<PERSON>, also known as the flowers king, is a french Twitch streamer. He plays various video games like Mario Kart, <PERSON><PERSON><PERSON> or Outer Wilds. On mondays, he organizes culture games with his viewers.", "summary_localized": {}, "type": {"primary": "Twitch", "secondary": "Video games"}, "type_localized": {}, "updated_at": {"__time__": "2020-05-13T16:47:13.470Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPonce (Twitch)-1615711670731.png?alt=media", "image_name": "Ponce (Twitch)-1615711670731.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://slowrun.fr/", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "IbCfi36YbjM4iurcAzlZ": {"created_at": {"__time__": "2020-03-28T23:30:22.751Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Futura", "release": {"before_common_era": false, "original": null}, "summary": "Futura is a french online newspaper created in 2001 which delivers news with a scientific approach. Its main themes are: sciences, technology, planet, health, housing. Futura wants to improve critical mind and bring fair information. An experts group work regularly with the newspaper.", "summary_localized": {}, "type": {"primary": "Newspaper", "secondary": "Sciences"}, "type_localized": {}, "updated_at": {"__time__": "2020-03-28T23:30:22.751Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFutura.png?alt=media&token=97fe325d-caf5-424c-baa2-19ef2967269b", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.futura-sciences.com/", "wikipedia": "https://fr.wikipedia.org/wiki/Futura_(portail_web)", "youtube": ""}, "__collections__": {}}, "IlLfdOqfjQC5MRWXtpXL": {"created_at": {"__time__": "2024-01-01T13:05:12.257Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-01-01T12:26:39.338Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2017-07-12T22:00:00.000Z"}}, "summary": "«<PERSON><PERSON><PERSON><PERSON><PERSON> zéro» is a french novel written by <PERSON>, published on 2017 by <PERSON> in Éditions Eyrolles. It's her first novel.\n\nIt tells the story of <PERSON><PERSON><PERSON>, chief financial officer in a startup, whose life is changed the day her friend asks for help. From this day, a long trip will begin.", "type": {"primary": "book", "secondary": "Novel"}, "updated_at": {"__time__": "2024-01-01T13:05:12.257Z"}, "urls": {"amazon": "https://www.amazon.fr/Kilom%C3%A8tre-z%C3%A9ro-chemin-du-bonheur/dp/229021051X", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FKilomètre zéro-1704114319359.jpg?alt=media", "imageName": "Kilomètre zéro-1704114319359.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Kilom%C3%A8tre_z%C3%A9ro_(livre)", "youtube": ""}, "__collections__": {}}, "J46YHoI22dy7wdjxw5S9": {"created_at": {"__time__": "2020-09-22T11:30:12.473Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "GJow", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "YouTuber", "secondary": "Gaming"}, "type_localized": {}, "updated_at": {"__time__": "2020-09-22T11:30:12.473Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/GJowTv", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGJow.png?alt=media&token=8ebe427e-e146-4fc6-8516-80d51935f4dd", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/gjow", "twitter": "https://twitter.com/GJowTv", "website": "https://fr.tipeee.com/gjow-tv", "wikipedia": "", "youtube": "https://www.youtube.com/c/GJowTv"}, "__collections__": {}}, "JaKsyI9BWCQC56LhNtRg": {"created_at": {"__time__": "2024-01-01T13:32:08.758Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-04T14:55:07.480Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Les vrais penseurs de notre temps ", "release": {"before_common_era": false, "original": {"__time__": "1989-08-03T22:00:00.000Z"}}, "summary": "From the Big Bang to Chinese philosophy, from the origins of language to liberal economics, from genetics to spirituality, the author of The New Wealth of Nations explores the characteristic intellectual theories of our time. Twenty-eight encounters, twenty-eight portraits - <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>... - take us on a discovery of the most topical debates in world scientific and philosophical thought.", "type": {"primary": "book", "secondary": "Intellectual life"}, "updated_at": {"__time__": "2024-01-01T13:32:08.758Z"}, "urls": {"amazon": "https://www.amazon.fr/vrais-penseurs-notre-temps/dp/2213023239", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLes vrais penseurs de notre temps -1704115930035.jpg?alt=media", "imageName": "Les vrais penseurs de notre temps -1704115930035.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "Jmv99MzmEt9w1fXanxk6": {"created_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "Feminists: What Were They Thinking?", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://www.netflix.com/browse?jbv=80216844&jbp=0&jbr=1", "youtube": ""}, "__collections__": {}}, "K2CFX3nNrTdSlQQr4uhu": {"created_at": {"__time__": "2020-05-02T23:15:19.281Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Succeeding by <PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "Succeeding is a business book writen by <PERSON> and published in 2003. The books talk about how to achieve high success, but it will constantly remind you to go for enough, not more; and to pursue what suits you, not just some goal chosen solely based on how much you think it will impress others.", "summary_localized": {}, "type": {"primary": "Book", "secondary": "Business"}, "type_localized": {}, "updated_at": {"__time__": "2020-05-02T23:15:19.281Z"}, "urls": {"affiliate": "https://www.amazon.com/Succeeding-<PERSON>-<PERSON>-2003-05-03/dp/B01FIWWAK2", "amazon": "", "facebook": "", "image": "https://cdn.shopify.com/s/files/1/0958/9924/files/succeedingcover200_large.gif?12664741428457080432", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "KdWqp2HeroHIWEd5xwaO": {"created_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "The Hitchhiker's Guide to the Galaxy", "release": {"before_common_era": false, "original": null}, "summary": "The Hitchhiker's Guide to the Galaxy is a comedy science fiction series created by <PERSON>. Originally a radio comedy broadcast on BBC Radio 4 in 1978, it was later adapted to other formats, including stage shows, novels, comic books, a 1981 TV series, a 1984 video game, and 2005 feature film.", "summary_localized": {}, "type": {"primary": "Novel series", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/b/bd/H2G2_UK_front_cover.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Hitchhiker's_Guide_to_the_Galaxy", "youtube": ""}, "__collections__": {}}, "KsX13dBnoynqQykFCaWq": {"created_at": {"__time__": "2024-07-22T19:47:46.490Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T19:43:51.266Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "We Will Rock You", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T19:43:51.266Z"}}, "summary": "\"We Will Rock You\" is a song by the British rock band Queen for their 1977 album News of the World, written by guitarist <PERSON>.[3] Rolling Stone ranked it number 330 of \"The 500 Greatest Songs of All Time\" in 2004,[4] and it placed at number 146 on the Songs of the Century list in 2001. In 2009, \"We Will Rock You\" was inducted into the Grammy Hall of Fame.", "type": {"primary": "music", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T19:47:46.490Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWe-Will-Rock-You-1721677667800.png?alt=media", "imageName": "We-Will-Rock-You-1721677667800.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/We_Will_Rock_You", "youtube": ""}, "__collections__": {}}, "LFdYQ6735kBoojPK60mo": {"created_at": {"__time__": "2024-05-26T14:32:37.133Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-05-26T14:27:25.381999Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Les Contemplations", "release": {"before_common_era": false, "original": {"__time__": "1855-12-31T23:00:00.000Z"}}, "summary": "\"Les Contemplations\" is a major poetry collection by <PERSON>, published in 1856. Here are some key facts about this seminal work:\n\n- It consists of 158 poems divided into six books, most written between 1841 and 1855, though some date back to 1830.\n\n- The collection is deeply autobiographical, with memory playing a central role. It serves as a poetic autobiography, with <PERSON> experimenting with the memoir genre in verse.[1][2] \n\n- Many poems pay tribute to <PERSON>'s daughter <PERSON><PERSON><PERSON><PERSON><PERSON>, who drowned accidentally in the Seine in 1843. Her tragic death profoundly marked the work and its structure.[1][3]\n\n- While continuing the lyricism of earlier works like Les Rayons et les Ombres, Les Contemplations also marked a shift towards a darker, more introspective poetry, representing a second poetic rebirth for <PERSON>.\n\n- The collection explores themes of love, joy, death, mourning, and a mystical faith. <PERSON> affirms his belief in the immortality of the soul and a form of metempsychosis.\n\n- Beyond the personal, Les Contemplations also serve as a mirror reflecting the collective soul, with a critical eye on 19th century society, its inequalities and misery.\n\n- It is considered one of the major works of French Romantic poetry, illustrating <PERSON>'s lyrical genius at its apex.", "type": {"primary": "book", "secondary": "Poetry"}, "updated_at": {"__time__": "2024-05-26T14:32:37.134Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLes-Contemplations-1716733966974.jpg?alt=media", "imageName": "Les-Contemplations-1716733966974.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Les_Contemplations", "youtube": ""}, "__collections__": {}}, "LXiJcnWZDEGh6Q8uD3wU": {"created_at": {"__time__": "2020-09-15T09:58:59.541Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Watchmen", "release": {"before_common_era": false, "original": null}, "summary": "Watchmen is an American superhero drama limited television series based on the 1986 DC Comics series Watchmen, created by <PERSON> and <PERSON>.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Superhero drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-09-15T09:58:59.541Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fwatchmen_tv_series.jpg?alt=media&token=301432ef-0ef1-4eda-bb3d-2c0c24597019", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.hbo.com/watchmen", "wikipedia": "https://en.wikipedia.org/wiki/Watchmen_(TV_series)", "youtube": ""}, "__collections__": {}}, "LXpNf3DiptFBXGYadidl": {"created_at": {"__time__": "2020-07-01T22:17:33.716Z"}, "id": "LXpNf3DiptFBXGYadidl", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-12-18T15:37:30.212Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2023-12-18T15:37:30.212Z"}}, "summary": "The story of the trial, conviction and acquittal of <PERSON> for the murder of an exchange student in Italy.", "summary_localized": {}, "type": {"primary": "film", "secondary": "Documentary"}, "type_localized": {}, "updated_at": {"__time__": "2020-07-01T22:17:33.716Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/f/f8/<PERSON>_<PERSON>_%28film%29.png", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80081155", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>_(film)", "youtube": ""}, "__collections__": {}}, "LoePEmZLbtKV7qwWokRI": {"created_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Histoire(s) de femmes", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.amazon.fr/150-lutte-pour-droits-femmes/dp/2035966299", "wikipedia": "https://www.amazon.fr/150-lutte-pour-droits-femmes/dp/2035966299", "youtube": ""}, "__collections__": {}}, "M5HNY6m2ZaJ5PSHGtLyy": {"created_at": {"__time__": "2020-03-30T01:15:56.583Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "La Cité de la peur", "release": {"before_common_era": false, "original": null}, "summary": "La Cité de la peur (French: \"The City of Fear\"), also known as Le film de Les Nuls (\"The Les Nuls Movie\"), is a 1994 French comedy film written by and starring <PERSON><PERSON>, <PERSON> and <PERSON>. The movie parodies big budget American films (Basic Instinct, Pretty Woman and The Terminator, among others, are directly spoofed) and relies heavily on puns and word play, which makes it somewhat inaccessible for non-French speakers. ", "summary_localized": {}, "type": {"primary": "Movie", "secondary": "Comedy"}, "type_localized": {}, "updated_at": {"__time__": "2020-03-30T01:15:56.583Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/a/ac/La_Cit%C3%A9_de_la_peur.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/La_Cit%C3%A9_de_la_peur", "youtube": ""}, "__collections__": {}}, "MKPuJ3u3iVxfR2sY8OJo": {"created_at": {"__time__": "2023-12-09T12:31:07.885Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-11-29T22:17:56.220Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2020-02-05T23:00:00.000Z"}}, "summary": "<PERSON><PERSON><PERSON><PERSON> is a track on the album «Notre-Dame-des-Sept-Douleurs» released on June 26, 2020 by Klô Pelgag. The track is produced by <PERSON><PERSON><PERSON><PERSON> and Pelgag and it lasts 4:29. You can hear in the music these different genres: Chamber pop, Baroque pop, Art pop, Electrorock.", "type": {"primary": "music", "secondary": "Baroque Pop"}, "updated_at": {"__time__": "2023-12-09T12:31:07.885Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRémora-1702125074034.jpg?alt=media", "imageName": "Rémora-1702125074034.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Notre-Dame-des-Sept-Douleurs_(album)", "youtube": "https://www.youtube.com/watch?v=Y9gJLi5uVHQ&pp=ygUGcmVtb3Jh"}, "__collections__": {}}, "MQneb7HYHNxOJPRegNTc": {"created_at": {"__time__": "2021-01-07T23:43:06.336Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Blood of Zeus", "release": {"before_common_era": false, "original": {"__time__": "2020-10-26T23:00:00.000Z"}}, "summary": "Chronicles the illegitimate son of <PERSON>, a young man tasked with saving heaven and earth despite the interference of a vengeful goddess and her monstrous forces.", "summary_localized": {}, "type": {"primary": "Animated TV series", "secondary": "Fantasy, action, adventure"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-07T23:43:06.336Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBlood of Zeus-1610062988568.jpg?alt=media", "image_name": "Blood of Zeus-1610062988568.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81001988", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Blood_of_Zeus", "youtube": ""}, "__collections__": {}}, "MvJZrmDoWPAAUMvdQzxO": {"created_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "<PERSON><PERSON><PERSON><PERSON>, or The Impostor, or The Hypocrite, first performed in 1664, is one of the most famous theatrical comedies by <PERSON><PERSON><PERSON>. The characters of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> are considered among the greatest classical theatre roles.", "summary_localized": {}, "type": {"primary": "Theatrical production", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Tartuffe", "youtube": ""}, "__collections__": {}}, "MwYZNv8T5euhLZfyDPq3": {"created_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "erreur du biomimétisme - DBY #43", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.340Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/1qvJDQTqSHY?t=558", "youtube": ""}, "__collections__": {}}, "N0t8MjZVYVl4mOYgt11I": {"created_at": {"__time__": "2023-09-19T13:22:47.585Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-17T14:51:33.414Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "L'esthétique - Ästhetik", "release": {"before_common_era": false, "original": {"__time__": "1832-08-16T22:00:00.000Z"}}, "summary": "Lectures on Aesthetics (LA; German: Vorlesungen über die Ästhetik, VÄ) is a compilation of notes from university lectures on aesthetics given by <PERSON> in Heidelberg in 1818 and in Berlin in 1820/21, 1823, 1826 and 1828/29. It was compiled in 1835 by his student <PERSON>, using <PERSON><PERSON>'s own hand-written notes and notes his students took during the lectures, but <PERSON><PERSON>'s work may render some of <PERSON><PERSON>'s thought more systematic than <PERSON><PERSON>'s initial presentation.\n\n<PERSON><PERSON> develops his account of art as a mode of absolute spirit that he calls \"the beautiful ideal\".", "type": {"primary": "other", "secondary": "Art"}, "updated_at": {"__time__": "2023-09-19T13:22:47.585Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FL'esthétique - Ästhetik-*************.jpg?alt=media", "imageName": "L'esthétique - Ästhetik-*************.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Lectures_on_Aesthetics", "youtube": ""}, "__collections__": {}}, "N4ddDYqIiTLavzcjuRhf": {"created_at": {"__time__": "2024-01-19T17:48:21.231Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-01T19:40:09.029Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "L'écriture de scénarios", "release": {"before_common_era": false, "original": {"__time__": "2018-09-10T22:00:00.000Z"}}, "summary": "With his recognized practical sense and rigorous precision, <PERSON><PERSON><PERSON> invites you to discover the secrets of dramaturgy, its fundamental rules, as well as the keys, tools and tricks that will help you approach screenwriting as simply as possible. From the idea for your film or series, to finding a producer, or even selling your project, this book guides you step by step, without ever losing you along the way. Numerous exercises and their corrected versions, as well as a well-stocked address book, are additional assets designed to give you the best chance of success.", "type": {"primary": "book", "secondary": "Cinema"}, "updated_at": {"__time__": "2024-01-19T17:48:21.231Z"}, "urls": {"amazon": "https://www.amazon.fr/L%C3%A9criture-sc%C3%A9nar<PERSON>-<PERSON>-<PERSON>/dp/2844811817", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FL'écriture de scénarios-1705686507944.jpg?alt=media", "imageName": "L'écriture de scénarios-1705686507944.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "NWeHwgOLoCW8e1Fl2qgC": {"created_at": {"__time__": "2024-07-22T18:23:02.635Z"}, "id": "NWeHwgOLoCW8e1Fl2qgC", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T18:20:27.032Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Game of Thrones", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T18:20:27.032Z"}}, "summary": "Game of Thrones is an American fantasy drama television series created by <PERSON> and <PERSON><PERSON> <PERSON><PERSON> for HBO. It is an adaptation of A Song of Ice and Fire, a series of fantasy novels by <PERSON>, the first of which is A Game of Thrones. The show premiered on HBO in the United States on April 17, 2011, and concluded on May 19, 2019, with 73 episodes broadcast over eight seasons.", "type": {"primary": "tv_series", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T18:23:02.635Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGame-of-Thrones-1721672583872.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "<PERSON> is the head of <PERSON> Stark, Lord of Winterfell, and Warden of the North.", "youtube": ""}, "__collections__": {}}, "OPkn7wOPcBEcLPdSrIF2": {"created_at": {"__time__": "2020-12-12T14:59:47.720Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2020-10-15T22:00:00.000Z"}}, "summary": "Rebecca is a 2020 British romantic thriller film directed by <PERSON> from a screenplay by <PERSON>, <PERSON>, and <PERSON>. The film is based on the 1938 novel of the same name by <PERSON>.\n\nThe plot is: while working as a companion to Mrs. <PERSON>, a rich American woman on holiday in Monte Carlo, an unnamed naïve young woman in her early 20s becomes acquainted with a wealthy Englishman, <PERSON>, a recent widower.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Romantic thriller"}, "type_localized": {}, "updated_at": {"__time__": "2020-12-12T14:59:47.720Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FRebecca-1607785191775.jpg?alt=media", "image_name": "Rebecca-1607785191775.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81002196", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_(2020_film)", "youtube": ""}, "__collections__": {}}, "OQEXBrfXRfSl8RRWTgPj": {"created_at": {"__time__": "2020-03-27T12:37:06.900Z"}, "id": "OQEXBrfXRfSl8RRWTgPj", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-12-18T13:46:50.104Z"}, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Accropolis", "release": {"before_common_era": false, "original": {"__time__": "2023-12-18T13:46:50.104Z"}}, "summary": "Inspired from video games platforms, Accropolis brings knowledge to french citizens who want to understand public issues and take back democracy. Shows are dynamically animated with a chat where everyone can interact with the host.", "summary_localized": {}, "type": {"primary": "website", "secondary": "Politics"}, "type_localized": {}, "updated_at": {"__time__": "2020-03-27T12:37:06.901Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/Accropolis", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fjeanmassiet.jpg?alt=media&token=7a0dfbe1-7d80-4353-bb92-56ad5f5ab9dd", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "https://www.twitch.tv/accropolis", "twitter": "https://twitter.com/Accropolis", "website": "https://accropolis.fr/", "wikipedia": "", "youtube": "https://www.youtube.com/channel/UCqv_wXmLSFtTDA39HQaLssQ"}, "__collections__": {}}, "OdjaGqT8IBIyqVXBoI5S": {"created_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "id": "OdjaGqT8IBIyqVXBoI5S", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-11-17T11:57:39.437Z"}, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Bref.", "release": {"before_common_era": false, "original": {"__time__": "2011-08-28T22:00:00.000Z"}}, "summary": "Bref. is a French television series created by <PERSON><PERSON>, cowritten with <PERSON>, and produced by <PERSON> for My Box Productions. The first episode was released on Canal+ 29 August 2011 and the show ended on 12 July 2012, following an announcement to that effect on 29 June 2012", "summary_localized": {}, "type": {"primary": "tv_series", "secondary": "Comedy"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBref.-1700222940463.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Bref", "youtube": "https://youtu.be/5pr4Q09-16k"}, "__collections__": {}}, "Opl5OJDTiPiJlRjdk07b": {"created_at": {"__time__": "2020-08-08T19:49:28.128Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Outsider", "release": {"before_common_era": false, "original": null}, "summary": "The Outsider is an American horror crime drama miniseries based on the 2018 novel of the same name by <PERSON>. It was ordered to series on December 3, 2018,[1] after being optioned as a miniseries by Media Rights Capital in June 2018.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Horror, Crime, Drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-08-08T19:49:28.128Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-Outsider.jpg?alt=media&token=5f1a3e47-4436-446d-9ed0-34905b5b3518", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "https://www.primevideo.com/detail/0GDRPSMGO410GZ30OGL0WNZPZD/ref=atv_sr_def_c_unkc__1_1_1?sr=1-1&pageTypeIdSource=ASIN&pageTypeId=B085L8BJN1&qid=1596958135", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Outsider_(miniseries)", "youtube": ""}, "__collections__": {}}, "PGb2cxpMi7qGqtd8cMfk": {"created_at": {"__time__": "2020-05-02T02:10:07.679Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON> <PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "Meet Joe Black is a 1998 American romantic fantasy film directed and produced by <PERSON>, and starring <PERSON>, <PERSON>, and <PERSON>. It was the second pairing of <PERSON> and <PERSON> after their 1994 film Legends of the Fall. The film received mixed reviews from critics. It grossed $143 million worldwide. ", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Romantic"}, "type_localized": {}, "updated_at": {"__time__": "2020-05-02T02:10:07.679Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/f/f5/Meet_<PERSON>_<PERSON>-_1998.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Meet_<PERSON>_<PERSON>", "youtube": ""}, "__collections__": {}}, "PIe5KUFEPvu7F4wdYDBU": {"created_at": {"__time__": "2020-04-06T22:46:00.841Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Extremely Wicked, Shockingly Evil and Vile", "release": {"before_common_era": false, "original": null}, "summary": "Extremely Wicked, Shockingly Evil and Vile is a 2019 American biographical crime thriller film about the life of serial killer <PERSON>. Directed by <PERSON> with a screenplay from <PERSON>, the film is based on <PERSON><PERSON>'s former girlfriend <PERSON>'s memoir, The Phantom Prince: My Life with <PERSON>. The film stars <PERSON><PERSON> as <PERSON><PERSON>, <PERSON> as <PERSON>, <PERSON><PERSON> as <PERSON><PERSON>'s wife <PERSON>, and <PERSON> as <PERSON>, the presiding judge at <PERSON><PERSON>'s trial. The title of the film is a reference to <PERSON><PERSON>'s remarks on <PERSON><PERSON>'s murders while sentencing him to death.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Biographical Crime Thriller"}, "type_localized": {}, "updated_at": {"__time__": "2020-04-06T22:46:00.841Z"}, "urls": {"affiliate": "https://www.netflix.com/title/81028570", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/8/8d/Extremely_Wicked%2C_Shockingly_Evil%2C_and_Vile_poster.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/81028570", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Extremely_Wicked,_Shockingly_Evil_and_Vile", "youtube": ""}, "__collections__": {}}, "PK8o5ytgEbu4OMtwrZ5R": {"created_at": {"__time__": "2021-03-02T21:48:31.257Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Behind Her Eyes", "release": {"before_common_era": false, "original": {"__time__": "2021-02-16T23:00:00.000Z"}}, "summary": "Behind Her Eyes is a British supernatural psychological thriller web series created by <PERSON>, based on the 2017 novel of the same name by <PERSON>, that premiered on Netflix on 17 February 2021. The plot tells a story of a single mother who enters a world of twisted mind games when she begins an affair with her psychiatrist boss while secretly befriending his mysterious wife.", "summary_localized": {}, "type": {"primary": "Streaming TV series", "secondary": "Supernatural psychological thriller"}, "type_localized": {}, "updated_at": {"__time__": "2021-03-02T21:48:31.258Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBehind Her Eyes-1614721714859.jpg?alt=media", "image_name": "Behind Her Eyes-1614721714859.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/80244630", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Behind_Her_Eyes_(British_TV_series)", "youtube": ""}, "__collections__": {}}, "Q6oLFgqIrbvDGZEKkwLZ": {"created_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "Cubicle Culture | #grindreel", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/RyBCpabJkuY", "youtube": ""}, "__collections__": {}}, "Q7n2H3z5hVfMClLXCLzD": {"created_at": {"__time__": "2020-06-30T20:18:21.942Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "True Story", "release": {"before_common_era": false, "original": null}, "summary": "A disgraced New York Times journalist is accused of lying in a cover article. His troubles increase when he learns that he is a victim of identity theft and the man who stole his name is in jail.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-06-30T20:18:21.942Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/9/92/True_Story_poster.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/True_Story_(film)", "youtube": ""}, "__collections__": {}}, "QVOJvZJq9V4PfafjN3Od": {"created_at": {"__time__": "2024-07-22T18:20:23.421Z"}, "id": "QVOJvZJq9V4PfafjN3Od", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T18:16:17.717Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Breaking Bad", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T18:16:17.717Z"}}, "summary": "Breaking Bad is an American crime drama television series created and produced by <PERSON> for AMC. Set and filmed in Albuquerque, New Mexico, the series follows <PERSON> (<PERSON>), an underpaid, dispirited high-school chemistry teacher struggling with a recent diagnosis of stage-three lung cancer. <PERSON> turns to a life of crime and partners with a former student, <PERSON> (<PERSON>), to produce and distribute methamphetamine to secure his family's financial future before he dies, while navigating the dangers of the criminal underworld. Breaking Bad premiered on AMC on January 20, 2008, and concluded on September 29, 2013, after five seasons consisting of 62 episodes.", "type": {"primary": "tv_series", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T18:20:23.421Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBreaking-Bad-1721672429969.png?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Breaking_Bad", "youtube": ""}, "__collections__": {}}, "QYeYoafXhWD451AsrIn1": {"created_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Nomade Digital Podcast", "release": {"before_common_era": false, "original": null}, "summary": "French podcast talking about taking back ownership on your time and finance. It's co-hosted by <PERSON> and <PERSON>.", "summary_localized": {}, "type": {"primary": "Podcast", "secondary": "Audio"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://marketingmania.fr/wp-content/uploads/2019/04/NomadeDigital.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://marketingmania.fr/nomadedigital/", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "QidJ1Q72BQPPMiK2rKER": {"created_at": {"__time__": "2020-11-05T11:44:48.969Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Simba", "release": {"before_common_era": false, "original": {"__time__": "2015-03-01T23:00:00.000Z"}}, "summary": "\"Simba\" is the 12th track on the PNL's album \"Que la famille\". The instrumental used is a “beat type” made by <PERSON><PERSON>zy Beats, and named Better Days.", "summary_localized": {}, "type": {"primary": "Music", "secondary": "RAP"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-05T11:44:48.969Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSimba-1604576692379.jpg?alt=media", "image_name": "Simba-1604576692379.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/watch?v=6NonS60QIJA"}, "__collections__": {}}, "QsfiTZaBWt2oeEFw6p9H": {"created_at": {"__time__": "2024-07-22T18:40:24.161Z"}, "id": "QsfiTZaBWt2oeEFw6p9H", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T18:37:50.651Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Friends", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T18:37:50.651Z"}}, "summary": "Friends is an American television sitcom created by <PERSON> and <PERSON>, which aired on NBC from September 22, 1994, to May 6, 2004, lasting ten seasons.[1] With an ensemble cast starring <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, the show revolves around six friends in their 20s and early 30s who live in Manhattan, New York City. The original executive producers were <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.", "type": {"primary": "tv_series", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T18:40:24.161Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFriends-1721673625141.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Friends", "youtube": ""}, "__collections__": {}}, "RhlFNHB06E97ef32csoq": {"created_at": {"__time__": "2020-06-17T09:25:07.262Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Soundtrack", "release": {"before_common_era": false, "original": null}, "summary": "Soundtrack is an American musical drama web television series created by <PERSON>, that premiered on Netflix on December 18, 2019.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Musical drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-06-17T09:25:07.262Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/7/74/Soundtrack_%28TV_series%29_Title_Card.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Soundtrack_(TV_series)", "youtube": ""}, "__collections__": {}}, "SAhGwyYN5dPNgwqnfhU8": {"created_at": {"__time__": "2020-07-01T22:50:01.795Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Art of Failure: An <PERSON>y on the Pain of Playing Video Games", "release": {"before_common_era": false, "original": null}, "summary": "We may think of video games as being \"fun,\" but in The Art of Failure, <PERSON><PERSON> claims that this is almost entirely mistaken. When we play video games, our facial expressions are rarely those of happiness or bliss. Instead, we frown, grimace, and shout in frustration as we lose, or die, or fail to advance to the next level. Humans may have a fundamental desire to succeed and feel competent, but game players choose to engage in an activity in which they are nearly certain to fail and feel incompetent. So why do we play video games even though they make us unhappy? <PERSON><PERSON> examines this paradox.", "summary_localized": {}, "type": {"primary": "Book", "secondary": "Video Games"}, "type_localized": {}, "updated_at": {"__time__": "2020-07-01T22:50:01.795Z"}, "urls": {"amazon": "https://www.amazon.com/Art-Failure-Playing-Playful-Thinking/dp/0262529955", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe%20Art%20of%20Failure.jpg?alt=media&token=cc5ba6f3-5487-47f0-90ce-683f2d2da033", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://mitpress.mit.edu/books/art-failure", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "T25eKWmWEu4UgV96QjjS": {"created_at": {"__time__": "2023-09-19T15:13:25.520Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-16T14:41:55.092Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Poetics (<PERSON>)", "release": {"before_common_era": false, "original": {"__time__": "2023-08-16T14:41:55.092Z"}}, "summary": "<PERSON>'s Poetics is the earliest surviving work of Greek dramatic theory and the first extant philosophical treatise to focus on literary theory.\n\nIn this text <PERSON> offers an account of ποιητική, which refers to poetry and more literally \"the poetic art,\" deriving from the term for \"poet; author; maker,\" ποιητής. <PERSON> divides the art of poetry into verse drama (comedy, tragedy, and the satyr play), lyric poetry, and epic.\n\nThe genres all share the function of mimesis, or imitation of life, but differ in three ways that <PERSON> describes:\n\n• Differences in music rhythm, harmony, meter, and melody.\n• Difference of goodness in the characters.\n• Difference in how the narrative is presented: telling a story or acting it out.\n\nThe surviving book of Poetics is primarily concerned with drama; the analysis of tragedy constitutes the core of the discussion.", "type": {"primary": "book", "secondary": "Dramatic theory"}, "updated_at": {"__time__": "2023-09-19T15:13:25.520Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPoetics (<PERSON>)-1695136412474.jpg?alt=media", "imageName": "Poetics (<PERSON>)-1695136412474.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Poetics_(<PERSON>)", "youtube": ""}, "__collections__": {}}, "TEcENVKCao7AN2wpMZFt": {"created_at": {"__time__": "2023-12-09T13:33:26.589Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-12-09T13:14:13.006Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON><PERSON>, <PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2008-08-24T22:00:00.000Z"}}, "summary": "«<PERSON><PERSON><PERSON>, <PERSON>» is the fifth track on the album «Metropolis: Suite I (The Chase)» by <PERSON><PERSON><PERSON>.\n\nReleased on August 24, 2007 on Bad Boy Records, and produced by <PERSON><PERSON><PERSON>, <PERSON> Z, and <PERSON>, the EP constitutes the first installment of <PERSON><PERSON><PERSON>'s seven-part Metropolis conceptual series.\n\nMetropolis: Suite I (The Chase) is the first installment of <PERSON><PERSON><PERSON>'s seven-part Metropolis conceptual series, inspired by <PERSON>'s science fiction classic film, Metropolis (1927). It follows a fictional tale of android <PERSON><PERSON><PERSON> who is mass-produced in the year 2719 for a market filled with severe social stratification.[citation needed] <PERSON><PERSON><PERSON> falls in love with a human, and is sentenced to disassembly.", "type": {"primary": "music", "secondary": "R&B"}, "updated_at": {"__time__": "2023-12-09T13:33:26.589Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSincerely, Jane-1702128812032.jpg?alt=media", "imageName": "Sincerely, Jane-1702128812032.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Metropolis:_Suite_I_(The_Chase)", "youtube": "https://www.youtube.com/watch?v=b_WhE7mBwK8"}, "__collections__": {}}, "TEfe1hDcfStQjMjZL8Qj": {"created_at": {"__time__": "2021-02-23T17:41:40.228Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Phantom Thread", "release": {"before_common_era": false, "original": {"__time__": "2017-12-24T23:00:00.000Z"}}, "summary": "Phantom Thread is a 2017 American historical drama film written and directed by <PERSON>, and starring <PERSON>, <PERSON> and <PERSON>. Set in 1950s London, it stars <PERSON><PERSON><PERSON> as an haute couture dressmaker who takes a young waitress, played by <PERSON><PERSON><PERSON>, as his muse.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Historical romance drama"}, "type_localized": {}, "updated_at": {"__time__": "2021-02-23T17:41:40.228Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPhantom Thread-1614102103927.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80195447", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.52b6390a-2a07-3ddf-ed48-a07adc6c732c", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Phantom_Thread", "youtube": "https://www.youtube.com/watch?v=FxxDPcj-4WA"}, "__collections__": {}}, "U5LTPfgfP1uoSECxwKGz": {"created_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Horizon - L'habit fait le moine", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/ZvURR83ohcg", "youtube": ""}, "__collections__": {}}, "UHOaflEgk7bEWxHr0u3t": {"created_at": {"__time__": "2020-08-15T13:12:40.263Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Euphoria", "release": {"before_common_era": false, "original": null}, "summary": "Euphoria is an American teen drama television series created by <PERSON>, loosely based on the Israeli miniseries of the same name. Euphoria follows a group of high school students through their experiences of sex, drugs, friendships, love, identity and trauma. The series stars <PERSON><PERSON><PERSON> and premiered on HBO on June 16, 2019.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Teen drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-08-16T13:12:40.000Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Feuphoria.jpg?alt=media&token=25af48b5-a810-4cf2-91f2-1831ad591bda", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/euphoriaHBO", "website": "https://www.hbo.com/euphoria", "wikipedia": "https://en.wikipedia.org/wiki/Euphoria_(American_TV_series)", "youtube": "https://www.youtube.com/c/EuphoriaHBO"}, "__collections__": {}}, "ULZxhPBq5Zqsqzz2DYN1": {"created_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "The Infinite Game: How to Lead in the 21st Century", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/3vX2iVIJMFQ?t=2910", "youtube": ""}, "__collections__": {}}, "UZX3ofmqAkMHYdFxyrJP": {"created_at": {"__time__": "2020-09-15T10:37:32.280Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Le RDV Tech", "release": {"before_common_era": false, "original": null}, "summary": "The RDV Tech is a fench news podcast on technologies, gadgets, internet.", "summary_localized": {}, "type": {"primary": "Podcast", "secondary": "Technologies"}, "type_localized": {}, "updated_at": {"__time__": "2020-09-16T10:37:32.000Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Frdv_tech.png?alt=media&token=2255192b-d7cf-4136-8525-c5257a081eea", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://frenchspin.fr/category/le-rdv-tech/", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "UeyxvyX0hxQ6ZlFmfh4e": {"created_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "Redmi K20 Pro Review: Incredible Value!", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/WpPw7lUXyI0?t=205", "youtube": ""}, "__collections__": {}}, "UpRUEfRAASxXYsOOZYoZ": {"created_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "Biased by Design - MIT Technology Review", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://www.technologyreview.com/s/602154/biased-by-design/", "youtube": ""}, "__collections__": {}}, "UqxYgigVqwtfb4w05eFa": {"created_at": {"__time__": "2021-02-10T21:05:57.553Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Vanilla Sky", "release": {"before_common_era": false, "original": {"__time__": "2002-01-22T23:00:00.000Z"}}, "summary": "Vanilla Sky is a 2001 American science fiction psychological thriller film directed, written, and co-produced by <PERSON>. It is an English-language adaptation of <PERSON>'s 1997 Spanish film Open Your Eyes, which was written by <PERSON><PERSON><PERSON><PERSON> and <PERSON>, with <PERSON><PERSON><PERSON><PERSON> reprising her role from the original film. The film has been described as \"an odd mixture of science fiction, romance and reality warp\".", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Science fiction psychological thriller"}, "type_localized": {}, "updated_at": {"__time__": "2021-02-10T21:05:57.553Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FVanilla Sky-1613600263078.jpg?alt=media", "image_name": "Vanilla Sky-1613600263078.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/60021786", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.d4ac18b8-4219-cfab-cd14-b1957e850d2c", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Vanilla_Sky", "youtube": "https://www.youtube.com/watch?v=jHfnCsmUBAo"}, "__collections__": {}}, "VNprD0LuVTKBqVvmTOuh": {"created_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "La mafia scientifique dont vous n'avez jamais entendu parler - DBY #53", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/rcgxY__YXEc?t=641", "youtube": ""}, "__collections__": {}}, "VSd4Um4RjHKL4hsTJGzl": {"created_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "The Nerdwriter", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "YouTube Channel", "secondary": "Video essay"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AGF-l7_hJSGF0JQvluUmAEs9tCstkd9SX47z4BetoQ=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/user/Nerdwriter1", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "VVZe1VQUs6YjDjbvYaAP": {"created_at": {"__time__": "2021-04-02T13:15:31.546Z"}, "id": "", "image": {"credits": {"artist": "Netflix", "beforeJC": false, "company": "Netflix", "date": {"__time__": "2021-03-31T23:56:51.879Z"}, "location": "", "name": "The One – Picture: Netflix", "url": "https://www.whats-on-netflix.com/wp-content/uploads/2021/02/the-one-poster.jpg"}}, "language": "en", "name": "The One", "release": {"before_common_era": false, "original": {"__time__": "2021-03-11T23:00:00.000Z"}}, "summary": "Love — and lies — spiral when a DNA researcher helps discover a way to find the perfect partner, and creates a bold new matchmaking service.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Science fiction drama"}, "type_localized": {}, "updated_at": {"__time__": "2021-04-02T13:15:31.546Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe One-1617369336140.jpg?alt=media", "image_name": "The One-1617369336140.jpg", "imdb": "https://www.imdb.com/title/tt13879466/", "instagram": "", "netflix": "https://www.netflix.com/title/80199029", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_One_(TV_series)", "youtube": ""}, "__collections__": {}}, "Vecxz5nfNmU93GBrmruv": {"created_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Space - Cosmos - Science Fiction"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.341Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AGF-l7-GhYv57eMcNAREO5ACzmsiyLGggSlm5Z2raQ=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/user/melodysheep", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "Vq4t26uQQVwu76i9jw1I": {"created_at": {"__time__": "2024-07-22T18:33:43.277Z"}, "id": "Vq4t26uQQVwu76i9jw1I", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T18:27:49.400Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Simpsons", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T18:27:49.400Z"}}, "summary": "The Simpsons is an American animated sitcom created by <PERSON> for the Fox Broadcasting Company. Developed by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, the series is a satirical depiction of American life, epitomized by the <PERSON> family, which consists of <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Set in the fictional town of Springfield, it caricatures society, Western culture, television, and the human condition.\n\n", "type": {"primary": "tv_series", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T18:33:43.277Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-Simpsons-1721673224686.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Simpsons", "youtube": ""}, "__collections__": {}}, "WSiN3J1h43LJ4A9QzNdd": {"created_at": {"__time__": "2023-12-12T23:30:21.124Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-09T14:22:59.398Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "L'Insoutenable légèreté de l'être - Nesnesitelná lehkost bytí", "release": {"before_common_era": false, "original": {"__time__": "1984-08-08T22:00:00.000Z"}}, "summary": "The Unbearable Lightness of Being is a 1984 novel by <PERSON>, about two women, two men, a dog, and their lives in the 1968 Prague Spring period of Czechoslovak history. Although written in 1982, the novel was not published until two years later, in a French translation", "type": {"primary": "novel", "secondary": "Philosophical fiction"}, "updated_at": {"__time__": "2023-12-12T23:30:21.124Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FL'Insoutenable légèreté de l'être - Nesnesitelná lehkost bytí-1702423822440.jpg?alt=media", "imageName": "L'Insoutenable légèreté de l'être - Nesnesitelná lehkost bytí-1702423822440.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Unbearable_Lightness_of_Being", "youtube": ""}, "__collections__": {}}, "Wy5lUVLWptZbUimkW8KV": {"created_at": {"__time__": "2024-07-22T19:39:29.454Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T19:34:52.384Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Toy Story", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T19:34:52.384Z"}}, "summary": "Toy Story is a 1995 American animated comedy film produced by Pixar Animation Studios for Walt Disney Pictures. The first installment in the franchise of the same name, it was the first entirely computer-animated feature film, as well as the first feature film from Pixar. The film was directed by <PERSON> (in his feature directorial debut), written by <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> based on a story by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, produced by <PERSON> and <PERSON>, and features the voices of <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>.", "type": {"primary": "film", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T19:39:29.454Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FToy-Story-1721677176656.jpg?alt=media", "imageName": "Toy-Story-1721677176656.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Toy_Story", "youtube": ""}, "__collections__": {}}, "X6udBLluPpaGOf3b5gZd": {"created_at": {"__time__": "2020-12-03T23:14:38.572Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Queen's Gambit", "release": {"before_common_era": false, "original": {"__time__": "2020-10-22T22:00:00.000Z"}}, "summary": "The Queen's Gambit is an American coming-of-age period drama streaming television miniseries based on <PERSON>'s 1983 novel of the same name, created for Netflix by <PERSON> and <PERSON>, and written and directed by the former. \n\nBeginning mid-1950s and proceeding into the 1960s, the story is about an orphaned chess prodigy on her rise to becoming the world's greatest chess player while struggling with emotional problems and drug and alcohol dependency.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Historical drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-12-03T23:14:38.572Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Queen's Gambit-1607037282219.jpg?alt=media", "image_name": "The Queen's Gambit-1607037282219.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/80234304", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Queen%27s_Gambit_(miniseries)", "youtube": ""}, "__collections__": {}}, "X9lux1mRP3mg8xr5wzgd": {"created_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Quel effet ça fait d'être une baleine ? 🐳 - DBY #36", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/vCEUxvz4Egs?t=258", "youtube": ""}, "__collections__": {}}, "XS5sHlB9kYuq3iveEhu9": {"created_at": {"__time__": "2020-03-28T23:29:40.789Z"}, "image": {"credits": {"artist": "", "beforeJC": false, "company": "", "date": 1616027799009, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "<PERSON><PERSON>gi<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2021-03-18T00:36:39.009Z"}}, "summary": "Hygiène Mentale is a french sciences channel created by <PERSON><PERSON><PERSON><PERSON>. His videos teach how to have a critical mind and how search for information. He also work on paranormal events.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Sciences"}, "type_localized": {}, "updated_at": {"__time__": "2020-03-28T23:29:40.789Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/HygieneMentale/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHygiène Mentale-1616028195178.jpg?alt=media", "image_name": "Hygiène Mentale-1616028195178.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/hygienementale", "website": "http://laelith.fr/Zet/Episodes/", "wikipedia": "https://fr.wikipedia.org/wiki/<PERSON>_<PERSON>", "youtube": "https://www.youtube.com/channel/UCMFcMhePnH4onVHt2-ItPZw"}, "__collections__": {}}, "XbVifXEWWXbO9rIyDNXg": {"created_at": {"__time__": "2020-03-28T23:29:28.797Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Covid-19 Conference (28/03/2020)", "release": {"before_common_era": false, "original": null}, "summary": "On Saturday, 28 March, 2020, the Prime Minister of France gave a conference with <PERSON>, the Health Minister, about the situation of the COVID-19. Several scientists came to explain how the country is fighting the situation and what to expect in the coming weeks.", "summary_localized": {}, "type": {"primary": "Conference", "secondary": "Health"}, "type_localized": {}, "updated_at": {"__time__": "2020-03-28T23:29:28.797Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCOVID-19.png?alt=media&token=32f07736-8ff6-4a1f-9510-759d54eb72f4", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.pscp.tv/w/1dRKZQXQWqDxB", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "XgPULwSWEKbZi2LeG175": {"created_at": {"__time__": "2020-11-03T21:44:02.622Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2018-11-24T23:00:00.000Z"}}, "summary": "\"Dirty John\" is an American true crime anthology television series, based on the podcast of the same name by <PERSON>, that premiered on November 25, 2018, on Bravo. \n\nIt explores the theme of love gone wrong, set against the backdrop of sunny Southern California. In the course of each season, viewers can follow a relationship from promising beginning to catastrophic end, raising the question of whether it's possible to really know someone, even the person one loves most. Through exploring the nuances and reframing the narratives of well-known cases, the series serves to remind the audience that there are two sides to every story, and people are often not quite as they seem.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "True-crime anthology"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-03T21:44:02.622Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDirty John-1604439845864.jpg?alt=media", "image_name": "Dirty John-1604439845864.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80241855", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_John_(TV_series)", "youtube": ""}, "__collections__": {}}, "Xs7HV4SumP7dsW3wsaM7": {"created_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Horizon - La soumission au costume", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/lzugeIXsLxc?t=284", "youtube": ""}, "__collections__": {}}, "XxF0n8LD1OrkeulmrdQ3": {"created_at": {"__time__": "2023-12-13T00:09:44.627Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-09T14:19:48.200999Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Rose Tattoo – La rose tato<PERSON>e", "release": {"before_common_era": false, "original": {"__time__": "1950-08-08T23:00:00.000Z"}}, "summary": "The Rose Tattoo is a three-act play written by <PERSON> in 1949 and 1950; after its Chicago premiere on December 29, 1950, he made further revisions to the play for its Broadway premiere on February 2, 1951, and its publication by New Directions the following month. A film adaptation was released in 1955.", "type": {"primary": "play", "secondary": "Drama"}, "updated_at": {"__time__": "2023-12-13T00:09:44.627Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Rose <PERSON>ttoo – La rose tato<PERSON>e-1702426185875.jpg?alt=media", "imageName": "The Rose Tattoo – La rose tato<PERSON>e-1702426185875.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_<PERSON>_<PERSON>o", "youtube": ""}, "__collections__": {}}, "Y1QesqH9QBulrBYwfIx9": {"created_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "The Witcher", "release": {"before_common_era": false, "original": null}, "summary": "The Witcher is an American fantasy drama web television series created by <PERSON> for Netflix. It is based on the book series of the same name by Polish writer <PERSON><PERSON><PERSON>.", "summary_localized": {}, "type": {"primary": "TV Series", "secondary": "Fantasy"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/2/23/The_Witcher_Title_Card.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://en.wikipedia.org/wiki/The_Witcher_(TV_series)", "wikipedia": "https://www.netflix.com/watch/80189685", "youtube": ""}, "__collections__": {}}, "YBLQ5YYWXGWE4JY2Ru0O": {"created_at": {"__time__": "2024-01-19T18:15:37.106Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-01T18:32:27.269999Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Portable <PERSON>", "release": {"before_common_era": false, "original": {"__time__": "1944-05-14T22:00:00.000Z"}}, "summary": "This collection ranges over the verse, stories, essays, and journalism of one of the twentieth century's most quotable authors.", "type": {"primary": "book", "secondary": "Poetry, Humorous fiction"}, "updated_at": {"__time__": "2024-01-19T18:15:37.106Z"}, "urls": {"amazon": "https://www.amazon.fr/Portable-<PERSON>-<PERSON>-non-massicot%C3%A9/dp/0143039539", "facebook": "", "image": "https://www.cdiscount.com/pdt2/5/3/2/1/700x700/9780143039532/rw/the-portable-dorothy-parker.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "Yj5cEusYhyvzqCyFofGZ": {"created_at": {"__time__": "2020-04-22T22:15:18.403Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "OkCupid", "release": {"before_common_era": false, "original": null}, "summary": "OkCupid is an American-based, internationally operating online dating, friendship, and social networking website that features multiple-choice questions in order to match members. It is supported by advertisements, by paying users who do not see ads, and by selling user data for data mining.", "summary_localized": {}, "type": {"primary": "Platform", "secondary": "Dating"}, "type_localized": {}, "updated_at": {"__time__": "2020-04-23T22:15:18.000Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fokcupid.png?alt=media&token=a8bef069-60b7-4b77-8e9d-9eebb5e821f0", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.okcupid.com/", "wikipedia": "https://en.wikipedia.org/wiki/OkCupid", "youtube": ""}, "__collections__": {}}, "YlG6bbnNj444rhS8iZ9U": {"created_at": {"__time__": "2024-07-22T18:27:15.989Z"}, "id": "YlG6bbnNj444rhS8iZ9U", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T18:23:59.835Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Star Trek", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T18:23:59.835Z"}}, "summary": "Star Trek is an American science fiction television series created by <PERSON> that follows the adventures of the starship USS Enterprise (NCC-1701) and its crew. It acquired the retronym of Star Trek: The Original Series (TOS) to distinguish the show within the media franchise that it began.", "type": {"primary": "tv_series", "secondary": "Movie"}, "updated_at": {"__time__": "2024-07-22T18:27:15.989Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStar-Trek-1721672837182.png?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Star_Trek:_The_Original_Series", "youtube": ""}, "__collections__": {}}, "Yr4pjLnCfcZ8Z43DS0IH": {"created_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Cette lutte microbiologique que nous perdons - DBY #56", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/LIDBmyT3YfA?t=365", "youtube": ""}, "__collections__": {}}, "YwsQXMojbf7HOTsY4HCb": {"created_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "The future we're building -- and boring | Elon Musk", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://www.youtube.com/watch?v=zIwLWfaAg-8", "youtube": ""}, "__collections__": {}}, "Z0tlEXjb5NJu8ZaoT2bz": {"created_at": {"__time__": "2024-01-22T09:03:07.535Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-01T16:48:15.596999Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Ma<PERSON> talks!", "release": {"before_common_era": false, "original": {"__time__": "2003-07-31T22:00:00.000Z"}}, "summary": "MAMET TALKS! by <PERSON> is a list of thoughts of <PERSON> about screenwriting.", "type": {"primary": "article", "secondary": "Cinema"}, "updated_at": {"__time__": "2024-01-22T09:03:07.535Z"}, "urls": {"amazon": "", "facebook": "", "image": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "ZJIz5dKogADrJTSJLYZV": {"created_at": {"__time__": "2020-03-31T15:58:27.846Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "National Assembly - Public session (31/03/2020)", "release": {"before_common_era": false, "original": null}, "summary": "The subjects in this public session were: the strategy to fight COVID-19, domestic violence, medication, and financial aid, among others. <PERSON><PERSON> and <PERSON> attended this session.", "summary_localized": {}, "type": {"primary": "Public session", "secondary": "Politics"}, "type_localized": {}, "updated_at": {"__time__": "2020-03-31T15:58:27.846Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAssemble%CC%81e%20Nationale.png?alt=media&token=684b9d8c-e258-43da-8fc3-a0c1e5d4952f", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "http://videos.assemblee-nationale.fr/video.8924502_5e833cf5c4761.1ere-seance--questions-au-gouvernement-31-mars-2020", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "ZbIJYvboVmiFnSE9uF1J": {"created_at": {"__time__": "2021-03-08T23:14:44.098Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Le Monde", "release": {"before_common_era": false, "original": {"__time__": "1944-12-18T23:00:00.000Z"}}, "summary": "Le Monde is a French daily afternoon newspaper. It is the main publication of Le Monde Group and reported an average circulation of 323,039 copies per issue in 2009, about 40,000 of which were sold abroad.", "summary_localized": {}, "type": {"primary": "Daily newspaper", "secondary": "Social liberalism, social democracy"}, "type_localized": {}, "updated_at": {"__time__": "2021-03-08T23:14:44.098Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/lemonde.fr", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe Monde-1615245285739.jpg?alt=media", "image_name": "Le Monde-1615245285739.jpg", "imdb": "", "instagram": "https://www.instagram.com/lemondefr/", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/lemondefr", "website": "https://www.lemonde.fr/", "wikipedia": "https://en.wikipedia.org/wiki/Le_Monde", "youtube": "https://www.youtube.com/channel/UCYpRDnhk5H8h16jpS84uqsA"}, "__collections__": {}}, "ZkIiH8Sa1nqcuWq7jZaE": {"created_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "<PERSON> G<PERSON>ule", "release": {"before_common_era": false, "original": null}, "summary": "Data Gueule is a french TV show and a web série. The program offers animated videos dealing with current events in a fun way, and condensed for educational purposes. Each episode attempts to reveal and decipher the mechanisms of society and their little-known aspects.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Education"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AGF-l7-luyqwwKEE2thUIUI2dpGn8VkKZnCwMEjQ0w=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/user/datagueule", "wikipedia": "https://fr.wikipedia.org/wiki/DataGueule", "youtube": ""}, "__collections__": {}}, "ZkqqMZts66zWrdvmroVz": {"created_at": {"__time__": "2020-11-02T13:21:32.251Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Layers of Fear 2", "release": {"before_common_era": false, "original": null}, "summary": "Layers of Fear 2 is a first-person psychological horror game with an emphasis on exploration and story. Players control a Hollywood actor who heeds the call of an enigmatic director to take on the lead role in a film shot aboard an ocean liner.", "summary_localized": {}, "type": {"primary": "Video games", "secondary": "Horror"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-02T13:21:32.251Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLayers of Fear 2-1604323296334.jpg?alt=media", "image_name": "Layers of Fear 2-1604323296334.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://layersoffear2.com/", "wikipedia": "https://en.wikipedia.org/wiki/Layers_of_Fear", "youtube": ""}, "__collections__": {}}, "Zn0BxJ5p5GhoEzoZko8r": {"created_at": {"__time__": "2023-12-13T00:34:45.579Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-09T13:58:22.903Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Interpretation of fairy tales – L'interprétation des contes de fées", "release": {"before_common_era": false, "original": {"__time__": "1970-08-08T23:00:00.000Z"}}, "summary": "A Jungian psychologist argues how careful analyses of fairy tales like <PERSON> and the Beast can lead to a deeper understanding of human psychology.\n\nOf the various types of mythological literature, fairy tales are the simplest and purest expressions of the collective unconscious and thus offer the clearest understanding of the basic patterns of the human psyche. Every people or nation has its own way of experiencing this psychic reality, and so a study of the world's fairy tales yields a wealth of insights into the archetypal experiences of humankind.", "type": {"primary": "book", "secondary": "Psychology"}, "updated_at": {"__time__": "2023-12-13T00:34:45.580Z"}, "urls": {"amazon": "https://www.amazon.com/Interpretation-Fairy-Tales-<PERSON>-<PERSON>-<PERSON>/dp/0877735263", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FInterpretation of fairy tales – L'interprétation des contes de fées-1702427688094.png?alt=media", "imageName": "Interpretation of fairy tales – L'interprétation des contes de fées-1702427688094.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "aONqOi70h4lpREVVxzOt": {"created_at": {"__time__": "2021-01-07T22:51:02.082Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Hades", "release": {"before_common_era": false, "original": {"__time__": "2020-09-16T22:00:00.000Z"}}, "summary": "Hades is a roguelike action role-playing video game developed and published by Supergiant Games. The game was released for Microsoft Windows, macOS, and Nintendo Switch on September 17, 2020, which followed an early access release from December 2018.", "summary_localized": {}, "type": {"primary": "Video game", "secondary": "Roguelite action RPG"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-07T22:51:02.082Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/supergiantgames", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHades-1610059865722.jpg?alt=media", "image_name": "Hades-1610059865722.jpg", "imdb": "", "instagram": "https://www.instagram.com/supergiantgames/", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/SupergiantGames", "website": "https://www.supergiantgames.com/games/hades/", "wikipedia": "https://en.wikipedia.org/wiki/Hades_(video_game)", "youtube": "https://www.youtube.com/user/supergiantgames"}, "__collections__": {}}, "as3MMbZUIh0v12KnTLnz": {"created_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "<PERSON> talks culture with Zappos CEO <PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/uqUx4BJ1ENY?t=504", "youtube": ""}, "__collections__": {}}, "asrAvYNOHqmJUprlFief": {"created_at": {"__time__": "2020-10-21T23:44:46.777Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Haunting of Bly Manor", "release": {"before_common_era": false, "original": {"__time__": "2020-10-08T22:00:00.000Z"}}, "summary": "The Haunting of Bly Manor is an American supernatural horror drama television series, created by <PERSON> for Netflix, and loosely based on <PERSON>'s work, particularly his 1898 novella The Turn of the Screw. It is the follow-up series to The Haunting of Hill House and the second entry in The Haunting anthology series.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Horror"}, "type_localized": {}, "updated_at": {"__time__": "2020-10-21T23:44:46.777Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fthe_haunting_of_bly_manor.jpg?alt=media&token=f94d3bd5-bfb8-4d78-a373-4325723c6efa", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81237854", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Haunting_of_Bly_Manor", "youtube": ""}, "__collections__": {}}, "b50yPPH51w3GjlimvUHq": {"created_at": {"__time__": "2024-07-22T18:37:41.519Z"}, "id": "b50yPPH51w3GjlimvUHq", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T18:35:09.894Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Big Bang Theory", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T18:35:09.894Z"}}, "summary": "The Big Bang Theory is an American television sitcom created by <PERSON> and <PERSON>, both of whom served as executive producers and head writers on the series, along with <PERSON>. It aired on CBS from September 24, 2007, to May 16, 2019, running for 12 seasons and 279 episodes.", "type": {"primary": "tv_series", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T18:37:41.519Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe-Big-Bang-Theory-1721673462653.png?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Big_Bang_Theory", "youtube": ""}, "__collections__": {}}, "b7JPY75HROfXibzkyG7s": {"created_at": {"__time__": "2024-01-01T13:24:53.589Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-12-19T13:46:10.736999Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "La Zone Du Dehors", "release": {"before_common_era": false, "original": {"__time__": "1998-12-31T23:00:00.000Z"}}, "summary": "La Zone du Dehors is a science-fiction novel by French writer <PERSON>, published in two volumes, Les Clameurs and La Volte by Cylibris in 1999, then in a single volume entitled La Zone du dehors by the same publisher in 2001.", "type": {"primary": "novel", "secondary": "Anticipation"}, "updated_at": {"__time__": "2024-01-01T13:24:53.589Z"}, "urls": {"amazon": "https://www.fnac.com/a8080394/<PERSON>-<PERSON><PERSON>o-La-Zone-du-Dehors", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa Zone Du Dehors-1704115501991.jpg?alt=media", "imageName": "La Zone Du Dehors-1704115501991.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://lavolte.net/livres/la-zone-du-dehors/", "wikipedia": "https://fr.wikipedia.org/wiki/La_Zone_du_Dehors", "youtube": ""}, "__collections__": {}}, "bWZ9EuSK2v1YH1a8EQtJ": {"created_at": {"__time__": "2021-01-08T00:15:06.093Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Celeste", "release": {"before_common_era": false, "original": {"__time__": "2018-01-24T23:00:00.000Z"}}, "summary": "Celeste is a 2018 platforming video game designed, directed and written by <PERSON> and programmed by <PERSON><PERSON> and <PERSON>. It is a fully-fleshed version of the 2016 game of the same name, which was made in four days solely by <PERSON><PERSON> and <PERSON> during a game jam.", "summary_localized": {}, "type": {"primary": "Video game", "secondary": "2D Platformer"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-08T00:15:06.093Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCeleste-1610064909414.png?alt=media", "image_name": "Celeste-1610064909414.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "http://www.celestegame.com/", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_(video_game)", "youtube": ""}, "__collections__": {}}, "bXW3dZegjwiOByrA0v7O": {"created_at": {"__time__": "2020-11-05T01:31:01.080Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "I'm Thinking of Ending Things", "release": {"before_common_era": false, "original": {"__time__": "2020-09-03T22:00:00.000Z"}}, "summary": "I'm Thinking of Ending Things (stylized in lowercase as i'm thinking of ending things) is a 2020 American psychological horror film written and directed by <PERSON>. The film is based on the 2016 novel of the same name by <PERSON> and stars <PERSON>, <PERSON>, <PERSON> and <PERSON>.\n\nThe plot is: Full of misgivings, a young woman travels with her new boyfriend to his parents' secluded farm.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Thriller/Horror"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-05T01:31:01.080Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FI'm Thinking of Ending Things-1604539864719.jpg?alt=media", "image_name": "I'm Thinking of Ending Things-1604539864719.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80211559", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/I%27m_Thinking_of_Ending_Things_(film)", "youtube": ""}, "__collections__": {}}, "dUn1p0VKGkVQ9WfCh3Cm": {"created_at": {"__time__": "2021-01-10T00:48:39.162Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Le parfum du bonheur est plus fort sous la pluie", "release": {"before_common_era": false, "original": {"__time__": "2017-05-02T22:00:00.000Z"}}, "summary": "\"Le parfum du bonheur est plus fort sous la pluie\" is a french novel written by <PERSON><PERSON>. The novel tells the story of <PERSON> who finds herself alone with her 4-years old boy one day, and decides to go living back with her parents. Each chapter takes the reader back to her previous life.", "summary_localized": {}, "type": {"primary": "Book", "secondary": "Novel"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-10T00:48:39.162Z"}, "urls": {"amazon": "https://www.amazon.com/parfum-bonheur-plus-pluie-French/dp/2253088110", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe parfum du bonheur est plus fort sous la pluie-1610239722430.jpg?alt=media", "image_name": "Le parfum du bonheur est plus fort sous la pluie-1610239722430.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.livredepoche.com/livre/le-parfum-du-bonheur-est-plus-fort-sous-la-pluie-9782253088110", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "dVjambJkaUW99WT91giI": {"created_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "Value Proposition and Knowing What You’re Worth: Crash Course Business - Entrepreneurship #3", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://youtu.be/m2IPvT920XM?t=397", "youtube": ""}, "__collections__": {}}, "deGLQF13YEy1USEhNFOR": {"created_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "<PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "<PERSON> is a YouTube channel talking about coding, entrepreneurship and personal development.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Coding - Personal Development"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/channel/UC61GK_nOLSJdzAK5hoR2mJA", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "drjNrIOe8E071bKrI4dT": {"created_at": {"__time__": "2020-06-17T09:20:18.165Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Dark (TV Series)", "release": {"before_common_era": false, "original": null}, "summary": "Dark is a German science fiction thriller web television series co-created by <PERSON><PERSON> and <PERSON><PERSON><PERSON>.[5][6][7] Set in the town of Winden, Germany, <PERSON> concerns the aftermath of a child's disappearance which exposes the secrets of, and hidden connections among, four estranged families as they slowly unravel a sinister time travel conspiracy which spans three generations. Throughout the series, <PERSON> explores the existential implications of time and its effects upon human nature.", "summary_localized": {}, "type": {"primary": "TV Show", "secondary": "<PERSON><PERSON>"}, "type_localized": {}, "updated_at": {"__time__": "2020-06-17T09:20:18.165Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDark%20(TV%20series).jpg?alt=media&token=1fe9d8dc-70dd-40ab-b854-762abe95aacd", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80100172", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Dark_(TV_series)", "youtube": ""}, "__collections__": {}}, "dw7BWf7DaNp3YWDZFJxW": {"created_at": {"__time__": "2020-03-22T23:00:00.000Z"}, "id": "dw7BWf7DaNp3YWDZFJxW", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-12-10T20:28:07.255Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "All the Bright Places", "release": {"before_common_era": false, "original": {"__time__": "2020-02-27T23:00:00.000Z"}}, "summary": "After meeting each other, two people struggle with the emotional and physical scars of their past. They discover that even the smallest moments can mean something.", "summary_localized": {}, "type": {"primary": "film", "secondary": "Drama/Romance"}, "type_localized": {}, "updated_at": {"__time__": "2020-03-22T23:00:00.000Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/8/8c/All_the_Bright_Places.jpeg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.netflix.com/watch/80208802?source=35", "wikipedia": "https://en.wikipedia.org/wiki/All_the_Bright_Places_(film)", "youtube": ""}, "__collections__": {}}, "e37dyuKmd4tEKZ0CVql6": {"created_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "Daybreak", "release": {"before_common_era": false, "original": null}, "summary": "Daybreak is an American post-apocalyptic comedy-drama web television series created by <PERSON> and <PERSON><PERSON>, based on the eponymous comic series by <PERSON>. The series follows the story of 17-year-old Canadian high school outcast <PERSON> searching for his missing British girlfriend <PERSON> in post-apocalyptic Glendale, California.", "summary_localized": {}, "type": {"primary": "TV Series", "secondary": "American comedy-drama series"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.342Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/4/47/Poster_for_Netflix_series_Daybreak.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.netflix.com/fr-en/title/80197462", "wikipedia": "https://en.wikipedia.org/wiki/Daybreak_(2019_TV_series)", "youtube": ""}, "__collections__": {}}, "e6Hg9D0pFJmE0MxQIeHY": {"created_at": {"__time__": "2020-06-17T09:01:37.416Z"}, "id": "e6Hg9D0pFJmE0MxQIeHY", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-12-10T20:25:24.986Z"}, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "ALT 236", "release": {"before_common_era": false, "original": {"__time__": "2023-12-10T20:25:24.986Z"}}, "summary": "ALT 236 is a french YouTube channel about art, cinema, strange pictures and mythology. ", "summary_localized": {}, "type": {"primary": "video", "secondary": "Art"}, "type_localized": {}, "updated_at": {"__time__": "2020-06-18T09:01:37.000Z"}, "urls": {"amazon": "", "facebook": "http://www.facebook.com/whatisalt236", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Falt_236.jpeg?alt=media&token=19786896-2a2b-42de-bb45-aaec3bb9c7cd", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "https://www.twitch.tv/whatisalt236", "twitter": "http://twitter.com/whatisalt236", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/channel/UC1KxoDAzbWOWOhw5GbsE-Bw"}, "__collections__": {}}, "enN66LfLnELNFR1uhjFp": {"created_at": {"__time__": "2020-03-27T12:46:10.364Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "<PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "<PERSON><PERSON><PERSON> is a french Twitch streamer. She plays various video games including adventure, FPS (First Person Shooter) or horror. Her streams contain also manual craft and cooking amoung other activites.", "summary_localized": {}, "type": {"primary": "Twitch", "secondary": "Video Games"}, "type_localized": {}, "updated_at": {"__time__": "2020-03-27T12:46:10.364Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fmaghla.jpg?alt=media&token=57f571dc-9b28-4915-b5af-b52741303259", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "primevideo": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.twitch.tv/maghla", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "fVlDXmwIwZaXzs3j62ae": {"created_at": {"__time__": "2020-07-30T15:30:31.626Z"}, "image": {"credits": {"artist": "", "beforeJC": false, "company": "truby.com", "date": 1615652377761, "location": "", "name": "", "url": "https://truby.com/wp-content/uploads/2013/05/Truby-book-jacket-300x449.jpg"}}, "language": "en", "name": "The Anatomy of Story", "release": {"before_common_era": false, "original": {"__time__": "2021-03-13T16:19:37.761Z"}}, "summary": "Based on the lessons in his award-winning class, Great Screenwriting, The Anatomy of Story draws on a broad range of philosophy and mythology, offering fresh techniques and insightful anecdotes alongside <PERSON><PERSON><PERSON>'s own unique approach for how to build an effective, multifaceted narrative. ", "summary_localized": {}, "type": {"primary": "Book", "secondary": "Screenplay"}, "type_localized": {}, "updated_at": {"__time__": "2020-07-30T15:30:31.627Z"}, "urls": {"amazon": "https://www.amazon.com/Anatomy-Story-Becoming-Master-Storyteller-ebook/dp/B0052Z3M8A", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Anatomy of Story-1615661667393.jpg?alt=media", "image_name": "The Anatomy of Story-1615661667393.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://truby.com/the-anatomy-of-story/", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "fYFIXUMisqn8ydIUUMcC": {"created_at": {"__time__": "2020-03-27T11:26:02.344Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Pixeland", "release": {"before_common_era": false, "original": null}, "summary": "Pixeland is an independent community for game developers, artists and students from all over the world. We welcome all members no matter how much experience they have.", "summary_localized": {}, "type": {"primary": "Community", "secondary": "Games"}, "type_localized": {}, "updated_at": {"__time__": "2020-03-27T11:26:02.344Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fpixeland.png?alt=media&token=25541d3c-561a-4aee-8b67-5beb36feb6e6", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://pixeland.io/", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "fxlq4cyADrNqkY1OIM82": {"created_at": {"__time__": "2020-04-08T06:47:12.684Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Enemy", "release": {"before_common_era": false, "original": null}, "summary": "<PERSON>, a college professor, spots an actor in a movie who looks exactly like him. <PERSON> tracks down his doppelganger and starts living his life secretly, which gives birth to a complex situation.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Thriller/Erotic thriller"}, "type_localized": {}, "updated_at": {"__time__": "2020-04-08T06:47:12.684Z"}, "urls": {"affiliate": "https://www.netflix.com/watch/70293661", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/0/0d/Enemy_poster.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Enemy_(2013_film)", "youtube": ""}, "__collections__": {}}, "gKHHjei2Lqlgh2hK4xUo": {"created_at": {"__time__": "2023-09-25T14:05:02.864Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-09-25T13:53:39.085Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "BEEF", "release": {"before_common_era": false, "original": {"__time__": "2023-04-05T22:00:00.000Z"}}, "summary": "Bee<PERSON> is a 2023 American comedy-drama television limited series created by Korean director <PERSON> for Netflix. It stars <PERSON> and <PERSON> as <PERSON> and <PERSON>, two strangers whose involvement in a road rage incident escalates into a prolonged feud. Appearing in supporting roles are <PERSON>, <PERSON>, <PERSON>, and <PERSON>.", "type": {"primary": "tv_series", "secondary": "Tragicomedy"}, "updated_at": {"__time__": "2023-09-25T14:05:02.864Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FBEEF-1695650709768.jpg?alt=media", "imageName": "BEEF-1695650709768.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/81447461", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.netflix.com/tudum/beef", "wikipedia": "https://en.wikipedia.org/wiki/Beef_(TV_series)", "youtube": ""}, "__collections__": {}}, "gT8gAQUkopjg6A4baFr6": {"created_at": {"__time__": "2024-07-22T15:39:07.445Z"}, "id": "gT8gAQUkopjg6A4baFr6", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T15:37:29.427Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Star Wars", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T15:37:29.427Z"}}, "summary": "Star Wars is an American epic space opera media franchise created by <PERSON>, which began with the eponymous 1977 film[a] and quickly became a worldwide pop culture phenomenon. The franchise has been expanded into various films and other media, including television series, video games, novels, comic books, theme park attractions, and themed areas, comprising an all-encompassing fictional universe.[b] Star Wars is one of the highest-grossing media franchises of all time.", "type": {"primary": "film", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T15:39:07.445Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStar-Wars-1721674761864.png?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Star_Wars", "youtube": ""}, "__collections__": {}}, "gvMlMrXoqYe41WRRczCQ": {"created_at": {"__time__": "2023-12-13T00:03:22.873Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-09T14:13:20.348Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Le sentiment même de soi - The Feeling of What Happens, Body and Emotion in the Making of Consciousness ", "release": {"before_common_era": false, "original": {"__time__": "1999-08-08T22:00:00.000Z"}}, "summary": "'The Feeling of What Happens will change your experience of yourself' New York Times Where do our emotions come from? What does it mean to be conscious? At its core, human consciousness is awareness of the feeling, experiencing self, the 'very thought of' oneself.", "type": {"primary": "book", "secondary": "Psychology"}, "updated_at": {"__time__": "2023-12-13T00:03:22.873Z"}, "urls": {"amazon": "https://www.amazon.fr/Feeling-What-Happens-Emotion-Consciousness/dp/0156010755", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe sentiment même de soi - The Feeling of What Happens, Body and Emotion in the Making of Consciousness -1702425804333.jpg?alt=media", "imageName": "Le sentiment même de soi - The Feeling of What Happens, Body and Emotion in the Making of Consciousness -1702425804333.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "hiYjuwHwNI6iKIiYjmfR": {"created_at": {"__time__": "2021-01-24T16:06:34.644Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "City Hunter: .357 Magnum", "release": {"before_common_era": false, "original": {"__time__": "1989-06-16T22:00:00.000Z"}}, "summary": "When a foreign dignitary is assassinated at a piano recital, and the pianist, along with her grandfather, are kidnapped, <PERSON> and his assistant <PERSON><PERSON> set off to rescue them.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Action, animation"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-24T16:06:34.644Z"}, "urls": {"amazon": "https://www.amazon.fr/City-Hunter-amour-destin-magnum/dp/B000N6U2P4", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fcity_hunter_amour_destin_magnum_357.jpg?alt=media&token=068138f5-57bc-461f-a2e1-e73d44f1ed6e", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/60027409", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "hxohvTzZAXYtjJbvihGl": {"created_at": {"__time__": "2020-11-05T08:08:55.564Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Quoi de neuf docteur ?", "release": {"before_common_era": false, "original": {"__time__": "2015-09-09T22:00:00.000Z"}}, "summary": "\"Quoi de neuf docteur ?\" is a french live stream hosted by <PERSON><PERSON><PERSON><PERSON><PERSON> (DFG) and broadcasted on Twitch. Replays are also available on YouTube. In his stream, D<PERSON><PERSON> invites a personality and ask intimate questions about their life, work and well-being. Previously on Eclypsia, the broadcast is now available on DFG personal channels since 2020.", "summary_localized": {}, "type": {"primary": "Live stream", "secondary": "Psychology/Social"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-05T08:08:55.564Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FQuoi%20de%20neuf%20docteur%20%3F.jpg?alt=media&token=87500e7c-6318-4fcd-9396-f679017877f8", "image_name": "Quoi de neuf docteur ?.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/DrFeelGood", "twitter": "", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/playlist?list=PLx3rX2jtuB9-yp2EOBpeV-aSQEL_zYcQ9"}, "__collections__": {}}, "iaUD8sGaUXhb9LPT7EIp": {"created_at": {"__time__": "2020-09-22T11:38:52.213Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "La chaîne de P.A.U.L", "release": {"before_common_era": false, "original": null}, "summary": "La chaîne de P.A.U.L is a french YouTube channel making documentary about celebrities or fictional characters like the Joker, <PERSON> and <PERSON><PERSON><PERSON>.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Documentary"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-01T23:00:00.000Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/PAUL-vid%C3%A9os-1488687308084372/?ref=bookmarks", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa_chaine_de_paul.jpg?alt=media&token=bfc134c8-ac42-49d2-97d2-8653a42044bc", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/PAUL_videos", "website": "https://fr.tipeee.com/p-a-u-l", "wikipedia": "", "youtube": "https://www.youtube.com/channel/UCS-NkJiYhYku2YtKi2W4p3w"}, "__collections__": {}}, "idSLBdXN2zp6Dzv9nvAB": {"created_at": {"__time__": "2023-09-19T14:41:46.182Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-16T22:22:54.930Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Panic", "release": {"before_common_era": false, "original": {"__time__": "2021-05-27T22:00:00.000Z"}}, "summary": "Panic is an American teen drama streaming television series created and written by <PERSON> based on her 2014 novel of the same name. The series stars <PERSON>, <PERSON>, and <PERSON>. The series premiered on Amazon Prime Video on May 28, 2021. In August 2021, the series was canceled after one season.\n\nThe plot: Every summer in a small Texas town, graduating seniors compete in a series of challenges, which they believe is their only chance to escape their circumstances and make their lives better.", "type": {"primary": "tv_series", "secondary": "Drama"}, "updated_at": {"__time__": "2023-09-19T14:41:46.182Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPanic-1695134511962.jpg?alt=media", "imageName": "Panic-1695134511962.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Panic_(TV_series)", "youtube": ""}, "__collections__": {}}, "ifRXNzltFmqE1vW5Q4rm": {"created_at": {"__time__": "2020-09-22T10:53:29.656Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Adventures of <PERSON><PERSON><PERSON> Finn", "release": {"before_common_era": false, "original": null}, "summary": "Adventures of Huckleberry Finn is a novel by <PERSON>, first published in the United Kingdom in December 1884 and in the United States in February 1885.", "summary_localized": {}, "type": {"primary": "Book", "secondary": "Novel"}, "type_localized": {}, "updated_at": {"__time__": "2020-09-22T10:53:29.657Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHuckleberry_Finn.jpg?alt=media&token=3a5f091c-b6e4-435e-b5ab-3514ecddcae8", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Adventures_of_<PERSON><PERSON><PERSON>_<PERSON>", "youtube": ""}, "__collections__": {}}, "ikqVo94cXW8B3HQ2t1Tq": {"created_at": {"__time__": "2024-01-01T14:18:28.345Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-04T14:48:25.322Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Préface aux pièces plaisantes", "release": {"before_common_era": false, "original": {"__time__": "1898-08-03T22:00:00.000Z"}}, "summary": "One of <PERSON>'s most glittering comedies, <PERSON> and the Man is a burlesque of Victorian attitudes to heroism, war and empire. In the contrast between <PERSON><PERSON><PERSON><PERSON>, the mercenary soldier, and the brave leader, <PERSON><PERSON><PERSON>, the true nature of valour is revealed. <PERSON> mocks deluded idealism in Candida, when a young poet becomes infatuated with the wife of a Socialist preacher. The Man of Destiny is a witty war of words between <PERSON> and a 'strange lady', while in the exuberant farce You Never Can Tell a divided family is reunited by chance. Although <PERSON> intended Plays Pleasant to be gentler comedies than those in their companion volume, Plays Unpleasant, their prophetic satire is sharp and provocative.", "type": {"primary": "book", "secondary": "Essay"}, "updated_at": {"__time__": "2024-01-01T14:18:28.345Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPréface aux pièces plaisantes-1704118718971.jpg?alt=media", "imageName": "Préface aux pièces plaisantes-1704118718971.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "ioQHKdtZBShXnQfm0p8V": {"created_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Unbelievable (miniseries)", "release": {"before_common_era": false, "original": null}, "summary": "Unbelievable is an American drama web television miniseries about a series of rapes in Washington and Colorado. It s based on the 2015 news article \"An Unbelievable Story of Rape\", written by <PERSON><PERSON> and <PERSON>, and originally published by ProPublica and The Marshall Project. The series received critical acclaim.", "summary_localized": {}, "type": {"primary": "TV miniseries", "secondary": "Drama"}, "type_localized": {"fr": {"primary": "Mini-série", "secondary": "<PERSON><PERSON>"}}, "updated_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/d/d5/Unbelievable_%28miniseries%29_Title_Card.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.netflix.com/watch/80153467", "wikipedia": "https://www.google.com/url?sa=t&rct=j&q=&esrc=s&source=web&cd=11&cad=rja&uact=8&ved=2ahUKEwi3vpCLkJ_nAhWMxYUKHVW9BGUQFjAKegQIARAB&url=https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FUnbelievable_(miniseries)&usg=AOvVaw1HDLT3QG-2RO_8OUSLv-3t", "youtube": ""}, "__collections__": {}}, "jh0hzbTXVBYnIIokkGGW": {"created_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "On the Genealogy of Morality", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/On_the_Genealogy_of_Morality", "youtube": ""}, "__collections__": {}}, "k1G7lM5R6RzsA1dN0J9S": {"created_at": {"__time__": "2020-12-12T15:25:48.690Z"}, "id": "k1G7lM5R6RzsA1dN0J9S", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-12-18T15:24:29.106Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Away", "release": {"before_common_era": false, "original": {"__time__": "2020-09-03T22:00:00.000Z"}}, "summary": "Away is an American science fiction drama streaming television series, starring <PERSON> and created by <PERSON> that premiered on Netflix on September 4, 2020. In October 2020, the series was canceled after one season...", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Science fiction drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-12-12T15:25:48.690Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAway-1607786752277.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80214512", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Away_(TV_series)", "youtube": ""}, "__collections__": {}}, "k6YkglV7p3naXWbrbUfB": {"created_at": {"__time__": "2020-04-11T09:25:09.014Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "Dribbble is a self-promotion and social networking platform for digital designers and creatives. It serves as a design portfolio platform, jobs and recruiting site and is one of the largest platforms for designers to share their work online. The company is fully remote with no headquarters.", "summary_localized": {}, "type": {"primary": "Social Networking Platform", "secondary": "Design"}, "type_localized": {}, "updated_at": {"__time__": "2020-04-11T09:25:09.014Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fdribbble-ball-icon.png?alt=media&token=9cf965c9-da27-4659-ac58-46514d34ea55", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://dribbble.com/", "wikipedia": "https://en.wikipedia.org/wiki/Drib<PERSON>", "youtube": ""}, "__collections__": {}}, "kXWM7T4vvemmfsPGT1uS": {"created_at": {"__time__": "2020-06-28T09:11:22.919Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "<PERSON><PERSON><PERSON>, a fading cinema superhero, plans to resurrect his career with a passionate Broadway production. However, during rehearsals, his co-star is injured forcing him to hire a new actor.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Black comedy-drama "}, "type_localized": {}, "updated_at": {"__time__": "2020-06-28T09:11:22.919Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/6/63/<PERSON><PERSON>_poster.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80000643", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.46ab8695-f544-89f1-405a-2a181068ae21", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Birdman_(film)", "youtube": "http://www.youtube.com/watch?v=WvyDo35E_eA"}, "__collections__": {}}, "kiQxBz7bZFuJ3dwNzYkd": {"created_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "Pirates of the Caribbean", "release": {"before_common_era": false, "original": null}, "summary": "Pirates of the Caribbean is a series of five fantasy swashbuckler films produced by <PERSON> and loosely based on <PERSON>'s eponymous theme park ride. Directors of the series include <PERSON>, <PERSON> and <PERSON> and <PERSON><PERSON><PERSON>.", "summary_localized": {}, "type": {"primary": "Film series", "secondary": "Fantasy"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://live.staticflickr.com/4465/37801865102_a19371138c_b.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://pirates.disney.com", "wikipedia": "https://en.wikipedia.org/wiki/Pirates_of_the_Caribbean_(film_series)", "youtube": ""}, "__collections__": {}}, "l5rGUZ4TBoOT3rhTKkcx": {"created_at": {"__time__": "2024-01-01T14:24:34.014Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-04T14:04:50.746Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Les visiteurs de Cannes", "release": {"before_common_era": false, "original": {"__time__": "1992-08-03T22:00:00.000Z"}}, "summary": "On the occasion of the 45th Cannes Film Festival, its general delegate, <PERSON>, decided to pay tribute to all those who, film after film, have brought the festival to life: the great directors from all over the world. Testimonials from <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>... and many others.", "type": {"primary": "book", "secondary": "Cinematography"}, "updated_at": {"__time__": "2024-01-01T14:24:34.015Z"}, "urls": {"amazon": "https://www.amazon.fr/visiteurs-Cannes-Cin%C3%A9astes-%C3%A0-loeuvre/dp/2218051877", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLes visiteurs de Cannes-1704119075511.jpg?alt=media", "imageName": "Les visiteurs de Cannes-1704119075511.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "lKYpl1Za88vYMYXdLKmt": {"created_at": {"__time__": "2020-06-04T10:05:16.701Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Sous couleur de jouer", "release": {"before_common_era": false, "original": null}, "summary": "\"Sous couleur de jouer\" is a book talking about the concepts behind \"gamification\".", "summary_localized": {}, "type": {"primary": "Book", "secondary": "Psychology, Game"}, "type_localized": {}, "updated_at": {"__time__": "2020-06-04T10:05:16.701Z"}, "urls": {"affiliate": "https://www.amazon.fr/Sous-couleur-jouer-m%C3%A9taphore-ludique/dp/2714303250", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSous%20couleur%20de%20jouer.jpg?alt=media&token=ef8a70d0-06dc-4d9b-a6d8-cd60f32665ee", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "lcGfwz23gurjeiijbWEj": {"created_at": {"__time__": "2021-03-02T23:07:54.845Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "La Nuit de la Culture", "release": {"before_common_era": false, "original": {"__time__": "2019-04-30T22:00:00.000Z"}}, "summary": "\"La Nuit de la Culture\" is a program hosted by the french streamer <PERSON><PERSON><PERSON> on Twitch. He watches and plays with his viewers to \"Question pour un champion\", a french TV program from France 3. Because he can interacts with the chat, they can dig a subject and better understand a cultural material.", "summary_localized": {}, "type": {"primary": "Twitch", "secondary": "Culture"}, "type_localized": {}, "updated_at": {"__time__": "2021-03-02T23:07:54.846Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLa Nuit de la Culture-1614726478718.jpg?alt=media", "image_name": "La Nuit de la Culture-1614726478718.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/etoiles", "twitter": "", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Etoiles_(streameur)", "youtube": ""}, "__collections__": {}}, "mIp6uSPBPOq6TEDFsqjH": {"created_at": {"__time__": "2021-01-24T13:27:36.346Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Other Boleyn Girl", "release": {"before_common_era": false, "original": {"__time__": "2008-02-14T23:00:00.000Z"}}, "summary": "Centred on the the court of <PERSON>, this historical drama presents a calculating <PERSON>, who usurps her older sister <PERSON> as the king's mistress and ultimately desires to be his new queen.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Romance, Drama"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-24T13:27:36.346Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Other Boleyn Girl-1611494860201.jpg?alt=media", "image_name": "The Other Boleyn Girl-1611494860201.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.caac56bd-ae9b-e92a-5559-5fb8824f9305", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Other_<PERSON><PERSON><PERSON>_Girl_(2008_film)", "youtube": "https://www.youtube.com/watch?v=VXaOTiQMLOc"}, "__collections__": {}}, "maG52taBquCAZSR8jLRI": {"created_at": {"__time__": "2020-12-23T12:13:08.043Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Tyger", "release": {"before_common_era": false, "original": {"__time__": "1793-12-31T23:50:39.000Z"}}, "summary": "\"The Tyger\" is a poem by the English poet <PERSON>, published in 1794 as part of his Songs of Experience collection. It has been the subject of both literary criticism and many adaptations, including various musical versions.", "summary_localized": {}, "type": {"primary": "Poem", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-12-23T12:13:08.043Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Tyger-1608725589382.jpg?alt=media", "image_name": "The Tyger-1608725589382.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Tyger", "youtube": ""}, "__collections__": {}}, "n2fptztAGYb2ZEjg7aaA": {"created_at": {"__time__": "2024-06-10T10:36:11.256Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-10T10:25:25.845Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Summer Game Fest", "release": {"before_common_era": false, "original": {"__time__": "2020-04-30T22:00:00.000Z"}}, "summary": "Summer Game Fest is an annual live video game event organized and hosted by game journalist <PERSON>.\n\n- It takes place over multiple live streams during the summer period in North America.\n\n- The main highlight is the \"main show\" which showcases upcoming major game releases through trailers and announcements. This is usually held on the first day.\n\n- After the main show, there is a \"Day of the Devs\" stream focused on highlighting upcoming indie games.\n\n- Other publisher-specific streams and events also occur in the days following the main show.\n\n- It was created in 2020 following the cancellation of major gaming events like E3 due to COVID-19.\n\nBusiness Model\n\n- Publishers pay fees to have trailers aired during the main show, with rates estimated around $250,000 for a 1-minute trailer in 2024.\n\n- A few \"free slots\" are reserved for smaller indie studios during the main show.\n\n- The 2023 event reportedly made around $9.65 million from trailer fees alone.", "type": {"primary": "other", "secondary": "Event"}, "updated_at": {"__time__": "2024-06-10T10:36:11.256Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FSummer-Game-Fest-1718015779871.jpg?alt=media", "imageName": "Summer-Game-Fest-1718015779871.jpg", "imdb": "", "instagram": "https://www.instagram.com/summergamefest", "netflix": "", "prime_video": "", "twitch": "https://www.twitch.tv/thegameawards", "twitter": "https://twitter.com/summergamefest", "website": "https://www.summergamefest.com/", "wikipedia": "https://en.wikipedia.org/wiki/Summer_Game_Fest", "youtube": "https://youtu.be/pZzia5NrMuU"}, "__collections__": {}}, "nDETybYAfo70HM9kxCX8": {"created_at": {"__time__": "2020-08-07T10:19:20.634Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "ZeratoR (Twitch)", "release": {"before_common_era": false, "original": null}, "summary": "The ZeratoR TV is a french Twitch channel animated by <PERSON><PERSON><PERSON><PERSON> and his team. The channel broadcast various content from ESport events to independent games discovery. There are also special events like the ZEvent, ZLan and Trackmania Cup.", "summary_localized": {}, "type": {"primary": "Twitch", "secondary": "Video games"}, "type_localized": {}, "updated_at": {"__time__": "2020-08-07T10:19:20.634Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/ZeratoR", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FZeratoR-Twitch.png?alt=media&token=9c88756e-43af-428d-8df4-209d4a27ada1", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/zerator", "twitter": "https://twitter.com/ZeratoR", "website": "https://www.zerator.com/", "wikipedia": "https://fr.wikipedia.org/wiki/ZeratoR", "youtube": "https://www.youtube.com/channel/UCZ_oIYI9ZNpOfWbpZxWNuRQ"}, "__collections__": {}}, "nZwYshUhpQ5ThBFeX1ka": {"created_at": {"__time__": "2024-07-22T09:12:21.096Z"}, "id": "nZwYshUhpQ5ThBFeX1ka", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T09:06:23.950Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T09:06:23.950Z"}}, "summary": "<PERSON> G<PERSON> is a 1994 American comedy-drama film directed by <PERSON> and written by <PERSON>. It is an adaptation of the 1986 novel of the same name by <PERSON>, and stars <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.", "type": {"primary": "film", "secondary": "Novel"}, "updated_at": {"__time__": "2024-07-22T09:12:21.096Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FForrest-Gump-1721675203501.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>", "youtube": ""}, "__collections__": {}}, "o66JmqugN8LRxACItylu": {"created_at": {"__time__": "2021-03-08T23:04:26.580Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2013-10-06T22:00:00.000Z"}}, "summary": "Carrie is a 2013 American supernatural horror drama film directed by <PERSON>. It is the third film adaptation (the others were in 1976 and 2002) of <PERSON>'s 1974 novel of the same name and the fourth film in the Carrie franchise. It's the story of <PERSON>, a disturbed religious fanatic, who sits alone in her home on her bed and gives birth to a baby girl. She intends to kill the infant but changes her mind. Years later, her daughter <PERSON>, a shy, unassertive girl, experiences her first menstrual period, while showering, after the gym at school.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Supernatural horror drama"}, "type_localized": {}, "updated_at": {"__time__": "2021-03-08T23:04:26.580Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCarrie-1615244670568.jpg?alt=media", "image_name": "Carrie-1615244670568.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/70251537", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_(2013_film)", "youtube": ""}, "__collections__": {}}, "oHymF5ymhdWvp9sQEdXv": {"created_at": {"__time__": "2020-06-04T10:40:36.389Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Holiday", "release": {"before_common_era": false, "original": null}, "summary": "The Holiday is a 2006 romantic comedy film written, produced and directed by <PERSON>. Co-produced by <PERSON>, it was filmed in both California and England, and stars <PERSON> and <PERSON> as <PERSON> and <PERSON>, two lovelorn women from opposite sides of the Atlantic Ocean, who arrange a home exchange to escape heartbreak during the Christmas and holiday season. <PERSON> and <PERSON> were cast as the film's leading men <PERSON> and <PERSON>, with <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> playing key supporting roles.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Romantic comedy"}, "type_localized": {}, "updated_at": {"__time__": "2020-06-04T10:40:36.389Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/6/60/Theholidayposter.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Holiday", "youtube": ""}, "__collections__": {}}, "oWn4faC960YMYlGomY64": {"created_at": {"__time__": "2020-11-04T22:11:09.011Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Chernobyl", "release": {"before_common_era": false, "original": {"__time__": "2019-05-05T22:00:00.000Z"}}, "summary": "Chernobyl is a 2019 historical drama television miniseries that revolves around the nuclear disaster of the same name in 1986 and the cleanup efforts that followed. The series was created and written by <PERSON> and directed by <PERSON>.\n\nThe plot is: In April 1986, the city of Chernobyl in the Soviet Union suffers one of the worst nuclear disasters in the history of mankind. Consequently, many heroes put their lives on the line to save Europe.", "summary_localized": {}, "type": {"primary": "TV miniseries", "secondary": "Historical drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-04T22:11:09.011Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FChernobyl-1604527872714.jpg?alt=media", "image_name": "Chernobyl-1604527872714.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "https://www.primevideo.com/dp/amzn1.dv.gti.32b68305-7d82-b7c6-e639-2951b785bedb", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Chernobyl_(miniseries)", "youtube": ""}, "__collections__": {}}, "odwo0CRwAylYtzlIlQRF": {"created_at": {"__time__": "2021-01-24T18:54:12.345Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Livre IV de l’Histoire des variations des églises protestantes", "release": {"before_common_era": false, "original": {"__time__": "1687-12-31T23:50:39.000Z"}}, "summary": "A work from <PERSON> about anti Anti-Protestanism written between 1682 and 1688. It has 15 books. After pastor <PERSON>'s answer, <PERSON><PERSON><PERSON> wrote a sequel: \"les Avertissements aux protestants sur les lettres du ministre Jurieu contre l'Histoire des variations\".", "summary_localized": {}, "type": {"primary": "Book", "secondary": "Religious"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-24T18:54:12.345Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLivre IV de l’Histoire des variations des églises protestantes-1611514456664.jpg?alt=media", "image_name": "Livre IV de l’Histoire des variations des églises protestantes-1611514456664.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Histoire_des_variations_des_%C3%89glises_protestantes", "youtube": ""}, "__collections__": {}}, "oqNIzZXvWF3H4OJLjpHH": {"created_at": {"__time__": "2023-10-21T14:09:21.774Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-10-19T17:24:07.509Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Changeling", "release": {"before_common_era": false, "original": {"__time__": "2023-10-19T17:24:07.509Z"}}, "summary": "The Changeling is an American horror fantasy television series created by <PERSON> and directed by <PERSON><PERSON> based on the novel of the same name by <PERSON> for Apple TV+. The series premiered on September 8, 2023, with the first three episodes.", "type": {"primary": "tv_series", "secondary": "Horror fantasy"}, "updated_at": {"__time__": "2023-10-21T14:09:21.774Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Changeling-1697897363020.jpg?alt=media", "imageName": "The Changeling-1697897363020.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/watch?v=i3jjAm2dBeo"}, "__collections__": {}}, "pJmkaqfPXJs1xurG7WVH": {"created_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "Mindvalley", "release": {"before_common_era": false, "original": null}, "summary": "Mindvalley is an online pseudoscientific New Age portal, founded by <PERSON><PERSON><PERSON> in 2003. Mindvalley offers both free and paid videos, courses, software, and services on New Age spirituality and self-help topics such as mindfulness, meditation, and \"personal growth\", fitness, and pseudoscientific topics such as spiritual energy, auras and energy medicine.", "summary_localized": {}, "type": {"primary": "Company", "secondary": "Mindfulness"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://i.ytimg.com/vi/AxEDOqlclgI/maxresdefault.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.mindvalley.com", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "q7bgM61FaJ8xe2FHMpwq": {"created_at": {"__time__": "2020-04-12T23:44:56.457Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Nice Guys", "release": {"before_common_era": false, "original": null}, "summary": "The Nice Guys is a 2016 American neo-noir crime black comedy film directed by <PERSON> and written by <PERSON> and <PERSON>. The film stars <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>. Set in 1977 Los Angeles, the film focuses on a private eye (<PERSON><PERSON><PERSON>) and a tough enforcer (<PERSON><PERSON>) who team up to investigate the disappearance of a teenage girl (<PERSON><PERSON><PERSON>). ", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Comedy/Action"}, "type_localized": {}, "updated_at": {"__time__": "2020-04-12T23:44:56.457Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/e/e9/The_Nice_Guys_poster.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Nice_Guys", "youtube": ""}, "__collections__": {}}, "q9ZX8iJIuDrzx1hqmlgO": {"created_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "L'évolution des idées en physique", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.amazon.fr/Lévolution-idées-physique-<PERSON>-<PERSON>/dp/2081373106/ref=tmm_pap_swatch_0?_encoding=UTF8&qid=&sr=", "wikipedia": "https://www.amazon.fr/Lévolution-idées-physique-<PERSON>-<PERSON>/dp/2081373106/ref=tmm_pap_swatch_0?_encoding=UTF8&qid=&sr=", "youtube": ""}, "__collections__": {}}, "qGRy904AyWRW77z34nbo": {"created_at": {"__time__": "2020-05-16T20:57:38.960Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Hollywood", "release": {"before_common_era": false, "original": null}, "summary": "Hollywood is an American drama web television miniseries released on May 1, 2020, on Netflix. The miniseries is about a group of aspiring actors and filmmakers during the Hollywood Golden Age in the post-World War II era trying to make their dreams come true. The series received mixed reviews from critics who praised the performances of the cast and production value, but criticized its tone, writing, and the artistic license taken.", "summary_localized": {}, "type": {"primary": "TV Show", "secondary": "Drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-05-16T20:57:38.960Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/commons/7/77/Hollywood_-_Netflix_series_title_card.png", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Hollywood_(miniseries)", "youtube": ""}, "__collections__": {}}, "qMG13EMdHApbx9zXvrAd": {"created_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "Dirty Biology", "release": {"before_common_era": false, "original": null}, "summary": "Sciences videos on mindfuck subject, dirty or just fun. Sometimes we talk about biology too.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Sciences - Biology"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-02T22:11:00.000Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDirty%20Biology.jpg?alt=media&token=9ab3b79e-ecca-4a20-8793-2b1eb8435b76", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/user/dirtybiology", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "r9HrOSAOEmxuN3kCfMo3": {"created_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "<PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "The Misanthrope, or the Cantankerous Lover is a 17th-century comedy of manners in verse written by <PERSON><PERSON><PERSON>. It was first performed on 4 June 1666 at the Théâtre du Palais-Royal, Paris by the King's Players.", "summary_localized": {}, "type": {"primary": "Play", "secondary": "Comedy of manners"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Misanthrope", "youtube": ""}, "__collections__": {}}, "rIJbthATDxrH8UfCQiI5": {"created_at": {"__time__": "2024-07-22T14:38:55.010Z"}, "id": "rIJbthATDxrH8UfCQiI5", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T14:35:24.378Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON> and the Prisoner of Azkaban", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T14:35:24.378Z"}}, "summary": "<PERSON> and the Prisoner of Azkaban is a 2004 fantasy film directed by <PERSON> from a screenplay by <PERSON>, based on the 1999 novel of the same name by <PERSON><PERSON> <PERSON><PERSON>. It is the sequel to <PERSON> and the Chamber of Secrets (2002) and the third instalment in the <PERSON> film series. The film stars <PERSON> as <PERSON>, alongside <PERSON> and <PERSON> as <PERSON>'s best friends <PERSON> and <PERSON><PERSON><PERSON> respectively. It chronicles <PERSON>'s third year at Hogwarts and his quest to uncover the truth about his past, including the connection recently-escaped Azkaban prisoner <PERSON> has to <PERSON> and his deceased parents.", "type": {"primary": "film", "secondary": "Book"}, "updated_at": {"__time__": "2024-07-22T14:38:55.010Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F<PERSON><PERSON><PERSON>-<PERSON>-and-the-Prisoner-of-Azkaban-1721674937660.jpg?alt=media", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>_and_the_Prisoner_of_Azkaban_(film)", "youtube": ""}, "__collections__": {}}, "rRIfxA7EcEiDIQwRVvcr": {"created_at": {"__time__": "2024-07-21T15:47:45.375Z"}, "id": "rRIfxA7EcEiDIQwRVvcr", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-17T19:31:33.967Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "WeCrashed", "release": {"before_common_era": false, "original": {"__time__": "2024-07-17T19:31:33.967Z"}}, "summary": "WeCrashed is an American drama miniseries that premiered on Apple TV+ on March 18, 2022. The series stars <PERSON> and <PERSON> as <PERSON> and <PERSON><PERSON><PERSON>, the real-life married couple at the heart of WeWork, a coworking space company which claimed a valuation of $47 billion (in an internally produced prospectus) in 2019, before crashing as a result of financial revelations. The series is based on the podcast WeCrashed: The Rise and Fall of WeWork by Wondery.\n\n", "type": {"primary": "tv_series", "secondary": "Podcast"}, "updated_at": {"__time__": "2024-07-21T15:47:45.375Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FWeCrashed-1721675589580.jpg?alt=media", "imageName": "WeCrashed-1721675589580.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/WeCrashed", "youtube": ""}, "__collections__": {}}, "rXgAOmXiXbwNPgwH9yNM": {"created_at": {"__time__": "2020-06-17T09:43:38.956Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The woods", "release": {"before_common_era": false, "original": null}, "summary": "The Woods is a Polish mystery thriller web television miniseries, based on the novel of the same name by <PERSON>. The series premiered on Netflix on 12 June 2020.", "summary_localized": {}, "type": {"primary": "TV show", "secondary": "Crime drama"}, "type_localized": {}, "updated_at": {"__time__": "2020-06-17T09:43:38.956Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe_Woods_TV_Miniseries.jpg?alt=media&token=af340296-7687-49b8-b838-1fbbcef9d029", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>_(miniseries)", "youtube": ""}, "__collections__": {}}, "rvPbXjr3QpJGcG2rBC4C": {"created_at": {"__time__": "2024-07-22T18:51:33.551Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T18:48:20.299Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Guardians of the Galaxy", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T18:48:20.299Z"}}, "summary": "Guardians of the Galaxy (retroactively referred to as Guardians of the Galaxy Vol. 1) is a 2014 American superhero film based on the Marvel Comics superhero team of the same name. Produced by Marvel Studios and distributed by Walt Disney Studios Motion Pictures, it is the 10th film in the Marvel Cinematic Universe (MCU). Directed by <PERSON>, who wrote the screenplay with <PERSON>, it features an ensemble cast including <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON> as the titular Guardians, along with <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. In the film, <PERSON> (<PERSON>) and a group of extraterrestrial criminals go on the run after stealing a powerful artifact.", "type": {"primary": "film", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T18:51:33.551Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FGuardians-of-the-Galaxy-1721674301222.jpg?alt=media", "imageName": "Guardians-of-the-Galaxy-1721674301222.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Guardians_of_the_Galaxy_(film)", "youtube": ""}, "__collections__": {}}, "rzhF1S4lWs16W5xhBRgo": {"created_at": {"__time__": "2023-09-19T14:18:46.290Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-17T14:08:25.532Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Tao, zen and existential psychotherapy", "release": {"before_common_era": false, "original": {"__time__": "1959-08-16T23:00:00.000Z"}}, "summary": "The purpose of this paper is to try to explain the psychotherapeutic process in a human life.", "type": {"primary": "paper", "secondary": "Metapshychiatry"}, "updated_at": {"__time__": "2023-09-19T14:18:46.290Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FTao, zen and existential psychotherapy-1695133132963.png?alt=media", "imageName": "Tao, zen and existential psychotherapy-1695133132963.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.jstage.jst.go.jp/article/psysoc/2/4/2_1959.236/_pdf/-char/ja", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "s841w8gbzwQa3mc6GM3G": {"created_at": {"__time__": "2024-01-01T14:30:29.366Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-03T00:46:57.802Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Crowded Room", "release": {"before_common_era": false, "original": {"__time__": "2023-06-08T22:00:00.000Z"}}, "summary": "The Crowded Room is an American psychological thriller television miniseries created by <PERSON><PERSON><PERSON> and inspired by the 1981 non-fiction novel «The Minds of <PERSON>» by <PERSON>. <PERSON>, <PERSON>, and <PERSON> lead a supporting cast that includes <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>.\n\nThe series follows <PERSON> (Holland) after he was arrested for his involvement in a New York City shooting in 1979. <PERSON> unveils his life through a series of interviews with interrogator <PERSON><PERSON> (<PERSON><PERSON><PERSON>), and slowly details to <PERSON><PERSON>, and the audience, his mysterious past that led him to the fateful incident.", "type": {"primary": "tv_series", "secondary": "Psychological thriller"}, "updated_at": {"__time__": "2024-01-01T14:30:29.366Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Crowded Room-1704119430996.jpg?alt=media", "imageName": "The Crowded Room-1704119430996.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.apple.com/fr/tv-pr/originals/the-crowded-room/", "wikipedia": "https://en.wikipedia.org/wiki/The_Crowded_Room", "youtube": "https://www.youtube.com/watch?v=4w1xZA7pX2c"}, "__collections__": {}}, "sDxcLfU3t7hJHDVic6M1": {"created_at": {"__time__": "2024-06-05T00:46:02.362Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-05T00:32:20.042Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Arte", "release": {"before_common_era": false, "original": {"__time__": "1992-05-29T22:00:00.000Z"}}, "summary": "ARTE is a European public service television channel dedicated to cultural programming. Here are some key facts about ARTE:\n\n- 🇫🇷🇩🇪 It was created in 1992 as a symbol of Franco-German friendship, initiated by French President <PERSON> and German Chancellor <PERSON>.\n\n- ARTE is a joint venture between France and Germany, with separate companies in each country contributing to programming and operations.\n\n- 📺 It broadcasts cultural programs focused on topics like arts, literature, philosophy, history, and sciences from a European perspective.\n\n- ARTE airs in French and German with subtitles in the other language, as well as the original language when possible.\n\n- In addition to its TV channel, ARTE has an online streaming platform called arte.tv offering live and catch-up content.\n\n- 🚀 It launched an online opera platform in 2018, streaming new opera productions from across Europe with subtitles in six languages.\n\n- ARTE Concert is its platform dedicated to streaming live music performances, festivals, and cultural events.\n\n- The channel is available across Europe via satellite, cable, and digital terrestrial television.\n\n- While viewership is higher in France than Germany, ARTE has won numerous prestigious awards for its programming over the years.\n", "type": {"primary": "video", "secondary": "TV"}, "updated_at": {"__time__": "2024-06-05T00:46:02.363Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/artetv", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FArte-1717548371782.jpg?alt=media", "imageName": "Arte-1717548371782.jpg", "imdb": "", "instagram": "https://www.instagram.com/artefr", "netflix": "", "prime_video": "", "twitch": "https://www.twitch.tv/artefr", "twitter": "https://twitter.com/ARTEfr", "website": "https://www.arte.tv", "wikipedia": "https://en.wikipedia.org/wiki/Arte", "youtube": "https://www.youtube.com/@arte"}, "__collections__": {}}, "sl8seDQNJWD3HSIFvRDt": {"created_at": {"__time__": "2020-04-06T19:38:53.758Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON> (Channel)", "release": {"before_common_era": false, "original": null}, "summary": "<PERSON> makes videos where he talks about game development and art. Two of his games are: 'Pinstripe' and 'Coma'.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Game"}, "type_localized": {}, "updated_at": {"__time__": "2020-04-06T19:38:53.758Z"}, "urls": {"affiliate": "https://www.youtube.com/user/thomasmbrush/", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2F%20Thomas%20Brush%20(Channel).png?alt=media&token=1312abbb-e0a7-428a-82cb-cce93d6cd19a", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "http://atmosgames.com/", "wikipedia": "", "youtube": "https://www.youtube.com/user/thomasmbrush/"}, "__collections__": {}}, "snPEi7pniYRKl27oJ0Vd": {"created_at": {"__time__": "2021-05-02T19:11:13.586Z"}, "id": "", "image": {"credits": {"artist": "", "beforeJC": false, "company": "", "date": {"__time__": "2021-04-19T23:08:42.874Z"}, "location": "", "name": "", "url": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRsFdNe6VSW3_Z3pzM3nf21LJ8i8W7zF3wdOR0QhxGsocjeCkwL"}}, "language": "en", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2017-10-29T23:00:00.000Z"}}, "summary": "Based on the 1996 <PERSON> novel of the same name, \"<PERSON><PERSON> Grace\" tells the story of young <PERSON>, a poor Irish immigrant and domestic servant in Upper Canada who is accused and convicted of the 1843 murder of her employer and his housekeeper. St<PERSON><PERSON> <PERSON> is also convicted of the crime. <PERSON><PERSON><PERSON><PERSON><PERSON> is hanged, but <PERSON> is sentenced to life in prison, leading her to become one of the most notorious women of the period in Canada. The story is based on actual 19th-century events.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Drama"}, "type_localized": {}, "updated_at": {"__time__": "2021-05-02T19:11:13.586Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FA<PERSON>s Grace-1619982677806.jpg?alt=media", "image_name": "<PERSON><PERSON>-1619982677806.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80119411", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(miniseries)", "youtube": ""}, "__collections__": {}}, "tCBIArT3RLkHqG64zYSk": {"created_at": {"__time__": "2021-04-05T11:30:48.298Z"}, "id": "", "image": {"credits": {"artist": "Netflix", "beforeJC": false, "company": "Netflix", "date": {"__time__": "2021-03-17T23:00:00.000Z"}, "location": "", "name": "Deadly illusions", "url": "https://www.pophorror.com/wp-content/uploads/2021/03/DeadlyIllusions1.png"}}, "language": "en", "name": "Deadly illusions", "release": {"before_common_era": false, "original": {"__time__": "2021-03-17T23:00:00.000Z"}}, "summary": "Deadly Illusions is a 2021 American thriller drama film written and directed by <PERSON> and starring <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Thriller drama"}, "type_localized": {}, "updated_at": {"__time__": "2021-04-05T11:30:48.298Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDeadly illusions-1617622252496.png?alt=media", "image_name": "Deadly illusions-1617622252496.png", "imdb": "https://www.imdb.com/title/tt7897330/", "instagram": "", "netflix": "https://www.netflix.com/title/81346196", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Deadly_Illusions", "youtube": ""}, "__collections__": {}}, "u7R66BiB6lzR3ZhqrR1a": {"created_at": {"__time__": "2020-11-05T08:28:11.650Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Stupid Economics", "release": {"before_common_era": false, "original": {"__time__": "2015-06-18T22:00:00.000Z"}}, "summary": "Stupid Economics is a french YouTube channel talking about economy with motion design, Monopoly money or investigations. The goal is to invite new people to be interested in the monetary mechanism.", "summary_localized": {}, "type": {"primary": "YouTube channel", "secondary": "Economy"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-05T08:28:11.650Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/Stupideconomics/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FStupid Economics-1604564895283.jpg?alt=media", "image_name": "Stupid Economics-1604564895283.jpg", "imdb": "", "instagram": "https://www.instagram.com/stupideconomics", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "https://www.twitch.tv/stupideconomics", "twitter": "https://twitter.com/stupid_eco", "website": "https://en.tipeee.com/stupid-economics", "wikipedia": "", "youtube": "https://www.youtube.com/channel/UCyJDHgrsUKuWLe05GvC2lng"}, "__collections__": {}}, "uS51Rhnm33DBX9OVUCHB": {"created_at": {"__time__": "2020-11-05T01:02:01.818Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Oops!...I Did It Again", "release": {"before_common_era": false, "original": {"__time__": "2000-03-26T22:00:00.000Z"}}, "summary": "“Oops!… I Did It Again” is a song recorded by <PERSON><PERSON><PERSON> for her second studio album, Oops!… I Did It Again (2000).\n\nIt was nominated for the Grammy Award for Best Female Pop Vocal Performance during the 2001 ceremony. Commercially, the track peaked at number nine on the U.S. Billboard Hot 100.\n\nProducer and writer <PERSON> intended this to be his version of <PERSON><PERSON>’s “Woman In Love.”\n\nLyrically, the song refers to a female who views love as a game, and she decides to use that to her advantage by playing with her lover's emotions. Its bridge features a dialogue which references the 1997 film Titanic.", "summary_localized": {}, "type": {"primary": "Music", "secondary": "Pop"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-05T01:02:01.818Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOops!...I Did It Again-1604538125262.jpg?alt=media", "image_name": "Oops!...I Did It Again-1604538125262.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Oops!..._I_Did_It_Again_(song)", "youtube": "https://www.youtube.com/watch?v=CduA0TULnow"}, "__collections__": {}}, "uhsRH9uAXwFrZezmbDmq": {"created_at": {"__time__": "2023-12-12T23:23:56.996Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-12-12T14:33:48.546Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Le son interdit", "release": {"before_common_era": false, "original": {"__time__": "2020-10-15T22:00:00.000Z"}}, "summary": "«Le son interdit» is a single released on October 16, 2020 by ZeratoR, DFG & MisterMV. It is part of a donation goal during ZEVENT – a caritative french event organized by ZeratoR.", "type": {"primary": "music", "secondary": "RAP"}, "updated_at": {"__time__": "2023-12-12T23:23:56.996Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FLe son interdit-1702423442933.jpg?alt=media", "imageName": "Le son interdit-1702423442933.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": "https://youtu.be/9GXrE6xHkeM"}, "__collections__": {}}, "uqYepBCESElUXAqAniYB": {"created_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "links": [], "name": "Reddit", "release": {"before_common_era": false, "original": null}, "summary": "Reddit is an American social news aggregation, web content rating, and discussion website. Registered members submit content to the site such as links, text posts, and images, which are then voted up or down by other members.", "summary_localized": {}, "type": {"primary": "Application", "secondary": "Social news aggregation"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FReddit-1689877118072.png?alt=media", "image_name": "Reddit-1689877118072.png", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.reddit.com/", "wikipedia": "https://en.wikipedia.org/wiki/Reddit", "youtube": ""}, "__collections__": {}}, "uvnXVA5cA0x1yntpELLM": {"created_at": {"__time__": "2020-12-23T12:12:51.855Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Divine Image", "release": {"before_common_era": false, "original": {"__time__": "1788-12-31T23:50:39.000Z"}}, "summary": "\"The Divine Image\" is a poem by the English poet <PERSON> from his book Songs of Innocence, not to be confused with \"A Divine Image\" from Songs of Experience. It was later included in his joint collection Songs of Innocence and of Experience.", "summary_localized": {}, "type": {"primary": "Poem", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-12-23T12:12:51.855Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Divine Image-1608725575591.jpg?alt=media", "image_name": "The Divine Image-1608725575591.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Divine_Image", "youtube": ""}, "__collections__": {}}, "vkF5W59xdoAiUgK3j3op": {"created_at": {"__time__": "2024-07-22T18:06:20.676Z"}, "id": "vkF5W59xdoAiUgK3j3op", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-22T18:00:27.104Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "How I Met Your Mother", "release": {"before_common_era": false, "original": {"__time__": "2024-07-22T18:00:27.104Z"}}, "summary": "", "type": {"primary": "tv_series", "secondary": ""}, "updated_at": {"__time__": "2024-07-22T18:06:20.676Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FHow-I-Met-Your-Mother-1721674652525.jpg?alt=media", "imageName": "How-I-Met-Your-Mother-1721674652525.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "wejSgUOeIDIusRJcL6we": {"created_at": {"__time__": "2021-01-10T00:23:40.926Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Insomnie avec...", "release": {"before_common_era": false, "original": {"__time__": "2013-09-12T22:00:00.000Z"}}, "summary": "\"Insomnie avec...\" is a french YouTube interview series presented by <PERSON> and produced by FranceTV Slash. Interviews are informal and take place in two beds in which the guest talks about their life and career.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Interview"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-10T00:23:40.926Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FInsomnie avec...-1610238225287.jpg?alt=media", "image_name": "Insomnie avec...-1610238225287.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/user/montelesonfrance4"}, "__collections__": {}}, "wi8c2dNdlyI6xzKmSSnQ": {"created_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": ["8u0M0hW5zj4ZXoE9913R"], "name": "La Semaine de 4 heures", "release": {"before_common_era": false, "original": null}, "summary": "", "summary_localized": {}, "type": {"primary": "", "secondary": ""}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.amazon.fr/4-Hour-Work-Week-Escape-Anywhere/dp/0091929113/ref=tmm_pap_swatch_0?_encoding=UTF8&qid=1563212900&sr=1-1", "wikipedia": "https://www.amazon.fr/4-Hour-Work-Week-Escape-Anywhere/dp/0091929113/ref=tmm_pap_swatch_0?_encoding=UTF8&qid=1563212900&sr=1-1", "youtube": ""}, "__collections__": {}}, "xIeANPEgyh7QdUn66Ake": {"created_at": {"__time__": "2020-10-23T14:01:16.861Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "<PERSON><PERSON><PERSON> puissa<PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "Femmes puissantes is a France Inter radio show on saturday, 12 p.m., hosted by <PERSON><PERSON><PERSON>. She receives a woman guest to discuss her life, projects, and goals.", "summary_localized": {}, "type": {"primary": "Radio show", "secondary": "Culture and society"}, "type_localized": {}, "updated_at": {"__time__": "2020-10-23T14:01:16.861Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FFemmes%20puissantes.jpg?alt=media&token=dbd8c401-45ff-4a1e-9b97-2339e7b71900", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.franceinter.fr/emissions/femmes-puissantes", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "xL8vtItj4rqUV6gj86p8": {"created_at": {"__time__": "2020-07-01T22:28:00.017Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Dead to Me", "release": {"before_common_era": false, "original": null}, "summary": "Dead to Me is an American dark comedy web television series created by <PERSON> and executively produced by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. The series premiered on May 3, 2019, on Netflix and stars <PERSON> and <PERSON> as two grieving women who bond during therapy.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Dark comedy"}, "type_localized": {}, "updated_at": {"__time__": "2020-07-01T22:28:00.017Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fdead_to_me.jpg?alt=media&token=3a232685-ef0d-4cc1-80b1-4e6090b2a59b", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/watch/80219707", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/Dead_to_Me_(TV_series)", "youtube": ""}, "__collections__": {}}, "xhv7s5J7cczcBMJwNBM9": {"created_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "links": [], "name": "<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "Une chaine réservée aux belles personnes, qui traite des plaisirs de cette vie. Le Q, entre autres. ", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Feel Good, Sexualité, Société"}, "type_localized": {}, "updated_at": {"__time__": "2020-02-01T22:11:00.343Z"}, "urls": {"affiliate": "", "amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/FzYNjNduHvG7UyziSSAWGZnA9QegjKB0LLGk0po4--F_Ovv7Tz1K15-8DojCBzOI8iXoAHZx3o8=w2560-fcrop64=1,00005a57ffffa5a8-k-c0xffffffff-no-nd-rj", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/channel/UCCNYGiTc3u_W1JhxXD72GdQ", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "yAoD2dzoSrbb68eOm6oQ": {"created_at": {"__time__": "2020-07-30T15:30:24.583Z"}, "id": "yAoD2dzoSrbb68eOm6oQ", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-12-18T13:16:11.456Z"}, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "<PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2023-12-18T13:16:11.456Z"}}, "summary": "Clararunaway is a french YouTube channel speaking about cinema and TV shows. From fun facts about Interstellar to how to deal with the death of an actor in a production, the channel cover different aspects of this art.", "summary_localized": {}, "type": {"primary": "video", "secondary": "Cinematography"}, "type_localized": {}, "updated_at": {"__time__": "2020-07-30T15:30:24.583Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fclararunaway.jpg?alt=media&token=ccb17c73-7ad1-43ec-956c-fd1029cb2693", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/clararunaway", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/c/clararunaway/"}, "__collections__": {}}, "yTLZPjfPDD07AD9ZUq2O": {"created_at": {"__time__": "2020-03-23T19:29:49.100Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "Balade Mentale is a french YouTube channel exploring the Universe in poetry. It explores topics like the ending of the Universe, stars' lifespan, or the Library of Babel.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Sciences"}, "type_localized": {}, "updated_at": {"__time__": "2020-03-23T19:29:49.100Z"}, "urls": {"affiliate": "https://en.tipeee.com/baladementale", "amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AATXAJyIaTK7HHmPeyfI35MF7EEG3yrMw43vazxx8A=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/channel/UCS_7tplUgzJG4DhA16re5Yg/featured", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "z2gmE4AfOjaZbnBirVvE": {"created_at": {"__time__": "2020-11-03T17:03:51.375Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Gift (Atiye)", "release": {"before_common_era": false, "original": {"__time__": "2019-12-26T23:00:00.000Z"}}, "summary": "The Gift (known in Turkey as At<PERSON><PERSON>) is a Turkish psychological thriller Netflix series starring <PERSON><PERSON>. It was written by <PERSON> and <PERSON><PERSON><PERSON>. The first season consists of 8 episodes and became available for streaming on Netflix on December 27, 2019.[1][2] The series is an adaptation of the novel <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON>. The show tells the story of a painter in Istanbul embarks on a personal journey as she unearths universal secrets about an Anatolian archaeological site and its link to her past.", "summary_localized": {}, "type": {"primary": "TV series", "secondary": "Psychological thriller"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-03T17:03:51.375Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Gift (Atiye)-1604423035059.jpg?alt=media", "image_name": "The Gift (Atiye)-1604423035059.jpg", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/81037848", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Gift_(Turkish_TV_series)", "youtube": ""}, "__collections__": {}}, "z7kVNMz45acZv0kXVYNE": {"created_at": {"__time__": "2024-01-19T21:58:41.411Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-01T16:53:49.558Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Positif n°102", "release": {"before_common_era": false, "original": {"__time__": "1969-07-31T23:00:00.000Z"}}, "summary": "Cinema review by <PERSON><PERSON><PERSON>.", "type": {"primary": "book", "secondary": "Cinema"}, "updated_at": {"__time__": "2024-01-19T21:58:41.411Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FPositif n°102-1705701522892.webp?alt=media", "imageName": "Positif n°102-1705701522892.webp", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}, "__collections__": {}}, "zknEA6J4kljNQB4LhgP0": {"created_at": {"__time__": "2021-01-07T23:30:37.525Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2013-07-17T22:00:00.000Z"}}, "summary": "<PERSON> is the channel of a french YouTuber of the same name, talking about psychology, philosophy, social, among other subjects. He has a videos serie on philosophy where he explains concepts with a modern take.", "summary_localized": {}, "type": {"primary": "YouTube", "secondary": "Philosophy, psychologie, social"}, "type_localized": {}, "updated_at": {"__time__": "2021-01-07T23:30:37.525Z"}, "urls": {"amazon": "", "facebook": "https://www.facebook.com/cyrusnorth/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyrus North-1610062241274.jpg?alt=media", "image_name": "Cyrus North-1610062241274.jpg", "imdb": "", "instagram": "https://www.instagram.com/cyrusnorth/", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "https://twitter.com/cyrusnorth", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Cyrus_<PERSON>", "youtube": "https://www.youtube.com/channel/UCah8C0gmLkdtvsy0b2jrjrw"}, "__collections__": {}}, "zse0xSvb3uVutu3MKi85": {"created_at": {"__time__": "2021-02-24T21:32:21.316Z"}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Malcolm & Marie", "release": {"before_common_era": true, "original": {"__time__": "2021-02-04T23:00:00.000Z"}}, "summary": "<PERSON> & Marie is a 2021 American black-and-white romantic drama film written, produced and directed by <PERSON>. The film stars <PERSON> and <PERSON><PERSON><PERSON> (who both also produced) as the title characters, a writer-director and his girlfriend, whose relationship is tested on the night of his latest film's premiere.", "summary_localized": {}, "type": {"primary": "Film", "secondary": "Black-and-white romantic drama"}, "type_localized": {}, "updated_at": {"__time__": "2021-02-24T21:32:21.316Z"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FMalcolm & Marie-1614202345522.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/81344370", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_%26_<PERSON>", "youtube": ""}, "__collections__": {}}, "zuMYSzliZhWjoBkpgkkc": {"created_at": {"__time__": "2020-11-05T01:15:23.293Z"}, "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Octopath Traveler", "release": {"before_common_era": false, "original": {"__time__": "2018-07-12T22:00:00.000Z"}}, "summary": "Octopath Traveler is a turn-based role-playing video game developed by Square Enix, in collaboration with Acquire. The game was released for the Nintendo Switch in July 2018, for Microsoft Windows in June 2019, and for Stadia in April 2020.", "summary_localized": {}, "type": {"primary": "Video game", "secondary": "Turn-based RPG"}, "type_localized": {}, "updated_at": {"__time__": "2020-11-05T01:15:23.293Z"}, "urls": {"amazon": "https://www.amazon.fr/Nintendo-0045496422158-Octopath-Traveler/dp/B07BLD5NTQ", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOctopath Traveler-1604538925226.jpg?alt=media", "image_name": "Octopath Traveler-1604538925226.jpg", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "tiktok": "", "twitch": "", "twitter": "", "website": "https://www.nintendo.com/games/detail/octopath-traveler-switch/", "wikipedia": "https://en.wikipedia.org/wiki/Octopath_Traveler", "youtube": ""}, "__collections__": {}}}}