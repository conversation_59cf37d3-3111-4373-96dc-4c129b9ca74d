{"meta": {"format": "JSON", "version": "1.1.0", "projectId": "memorare-98eee", "resourcePath": ["drafts"], "recursive": false, "creationTime": 1752640070, "app": "firefoo"}, "data": {"0FddufQsxi6LjlI7tiZc": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-06-02T23:22:30.702Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-06-02T23:22:30.702Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-06-02T23:22:30.702Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-06-02T23:24:08.257Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Reframe the goals of others so they align with your own.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-06-02T23:22:30.702Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Pantheon ", "release": {"before_common_era": false, "original": {"__time__": "2023-06-02T23:22:30.702Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "language": true, "psychology": true, "social": true}, "updated_at": {"__time__": "2023-06-02T23:24:08.257Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-06-02T23:24:08.257Z"}}, "__collections__": {}}, "0HkYAjhjhSObsu2J6GHR": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-06-09T18:53:47.821Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-09T18:53:47.821Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-06-09T18:54:40.590Z"}, "language": "en", "name": "Champions keep playing until they get it right", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-09T18:53:47.821Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-06-09T18:53:47.821Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-06-10T09:54:04.703Z"}, "user": {"id": "p4PWrK73GUWam8wxaOmE64zKFR52"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-06-09T18:54:40.590Z"}}, "status": "", "updated_at": {"__time__": "2024-06-09T18:54:40.590Z"}}, "__collections__": {}}, "2P9H3V66bRxeHVOFdHb7": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T23:41:32.122Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T23:41:32.122Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "<PERSON><PERSON><PERSON>", "name": "Local man", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-29T23:44:01.469Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Evil only thrives in the house where it is welcome.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Things heard and seen", "release": {"before_common_era": false, "original": {"__time__": "2021-05-29T21:59:21.354Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"spiritual": true}, "updated_at": {"__time__": "2021-05-29T23:44:01.469Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-29T23:44:01.469Z"}}, "__collections__": {}}, "2b7SqJ6L2K3YyZchua3Q": {"author": {"birth": {"before_common_era": false, "city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Germany", "date": {"__time__": "2023-08-01T15:28:47.449Z"}}, "death": {"before_common_era": false, "city": "Weimar", "country": "Germany", "date": {"__time__": "2023-08-01T15:28:47.449Z"}}, "from_reference": {"id": "", "name": ""}, "id": "zROlefQfE7E9w04wcFUV", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-01T15:28:47.449Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "Philosopher", "name": "<PERSON>", "summary": "<PERSON> was a German philosopher who became one of the most influential of all modern thinkers. His attempts to unmask the motives that underlie traditional Western religion, morality, and philosophy deeply affected generations of theologians, philosophers, psychologists, poets, novelists, and playwrights.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FF<PERSON><PERSON> Nietzsche-1614727502837.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>", "youtube": ""}}, "created_at": {"__time__": "2023-08-01T15:29:32.583Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Certains croient profond ce dont ils ne voient pas le fond.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-01T15:26:54.322Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Le gai savoir", "release": {"before_common_era": false, "original": {"__time__": "1882-07-31T22:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "poetry": true, "psychology": true}, "updated_at": {"__time__": "2023-08-01T15:29:32.583Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-08-01T15:29:32.583Z"}}, "__collections__": {}}, "3Cvp4cnhWR800pEOpzk2": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-06-07T19:24:35.436Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-07T19:24:35.436Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON><PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-06-07T19:26:29.534Z"}, "language": "en", "name": "If it rains, you'll be the first to know.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-07T19:24:35.436Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON> and the Goblet of Fire", "release": {"before_common_era": false, "original": {"__time__": "2024-06-07T19:24:35.436Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-06-07T19:26:30.782Z"}, "user": {"id": "p4PWrK73GUWam8wxaOmE64zKFR52"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-06-07T19:26:29.534Z"}}, "status": "", "updated_at": {"__time__": "2024-06-07T19:26:29.534Z"}}, "__collections__": {}}, "3DW7IfTjnNMiUro2WqMe": {"author": {"birth": {"before_common_era": false, "city": "Boston, Massachusetts", "country": "United States", "date": {"__time__": "2023-01-14T23:50:37.444Z"}}, "death": {"before_common_era": false, "city": "Church Home & Hospital, Baltimore, Maryland", "country": "United States", "date": {"__time__": "2023-01-14T23:50:37.444Z"}}, "from_reference": {"id": "", "name": ""}, "id": "Mgy3JSjy83idrrzp26lP", "image": {"credits": {"artist": "Restored by <PERSON><PERSON> and <PERSON>", "before_common_era": false, "company": "", "date": {"__time__": "1849-05-31T22:00:00.000Z"}, "location": "", "name": "1849 \"Annie\" daguerreotype of Poe", "url": "https://en.wikipedia.org/wiki/<PERSON>?oldformat=true#/media/File:<PERSON>_<PERSON>,_circa_1849,_restored,_squared_off.jpg"}}, "is_fictional": false, "job": "American writer", "name": "<PERSON>", "summary": "<PERSON> was an American writer, poet, editor, and literary critic. <PERSON> is best known for his poetry and short stories, particularly his tales of mystery and the macabre.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FEd<PERSON>-1619982678027.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>", "youtube": ""}}, "created_at": {"__time__": "2023-01-14T23:52:15.223Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Ne croyez pas ce que vous entendez, et la moitié de ce que vous voyez.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-14T23:49:35.762Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Wednesday", "release": {"before_common_era": false, "original": {"__time__": "2023-01-14T23:49:35.762Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "poetry": true}, "updated_at": {"__time__": "2023-01-14T23:52:15.223Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-01-14T23:52:15.223Z"}}, "__collections__": {}}, "3qOBUu42W0kZaY2j9s0N": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-31T03:10:31.468Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-31T03:10:31.468Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-31T03:10:31.468Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>hams", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-31T03:12:11.088Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "La patience, c'est regarder l'épine et voir la rose.", "reference": {"id": "z2gmE4AfOjaZbnBirVvE", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-31T03:12:01.656Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Gift (Atiye)", "release": {"before_common_era": false, "original": {"__time__": "2019-12-26T23:00:00.000Z"}}, "summary": "The Gift (known in Turkey as At<PERSON><PERSON>) is a Turkish psychological thriller Netflix series starring <PERSON><PERSON>. It was written by <PERSON> and <PERSON><PERSON><PERSON>. The first season consists of 8 episodes and became available for streaming on Netflix on December 27, 2019.[1][2] The series is an adaptation of the novel <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON>. The show tells the story of a painter in Istanbul embarks on a personal journey as she unearths universal secrets about an Anatolian archaeological site and its link to her past.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Gift (Atiye)-1604423035059.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/81037848", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Gift_(Turkish_TV_series)", "youtube": ""}}, "topics": {"biology": true, "introspection": true, "metaphor": true}, "updated_at": {"__time__": "2021-07-31T03:12:11.088Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-31T03:12:11.088Z"}}, "__collections__": {}}, "40SN2xxsHYhoC6fyWQCg": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-19T03:00:03.046Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-19T03:00:03.046Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-19T03:00:03.046Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "Pilote de ligne", "name": "<PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-09-19T03:01:51.758Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": " Un problème à la fois.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-19T03:00:03.046Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Into the Night", "release": {"before_common_era": false, "original": {"__time__": "2021-09-19T03:00:03.046Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"psychology": true}, "updated_at": {"__time__": "2021-09-19T03:01:51.758Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-09-19T03:01:51.758Z"}}, "__collections__": {}}, "4p69DXZy7WQFJNyqIVqc": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-06-07T19:14:35.986Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-07T19:14:35.986Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-06-07T19:15:10.640Z"}, "language": "en", "name": "You’re a supernova", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-07T19:14:35.986Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "WeCrashed", "release": {"before_common_era": false, "original": {"__time__": "2024-06-07T19:14:35.986Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-06-07T19:15:12.487Z"}, "user": {"id": "p4PWrK73GUWam8wxaOmE64zKFR52"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-06-07T19:15:10.641Z"}}, "status": "", "updated_at": {"__time__": "2024-06-07T19:15:10.641Z"}}, "__collections__": {}}, "4tSmjndYB6lbyU00VTYf": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-26T03:21:58.662Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-26T03:21:58.662Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-26T03:21:58.662Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>. <PERSON> the Divine", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-01-26T03:25:48.809Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "None of the dead cone back. But some stay.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-26T03:21:58.662Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "MaleVolent", "release": {"before_common_era": false, "original": {"__time__": "2023-01-26T03:21:58.662Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"spiritual": true}, "updated_at": {"__time__": "2023-01-26T03:25:48.809Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-01-26T03:25:48.809Z"}}, "__collections__": {}}, "4ungMmAEiD4MZbQ9T9Zh": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-11T01:49:52.834Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-11T01:49:52.834Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-11T01:49:52.834Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-03-11T01:55:28.440Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le seul problème philosophique sérieux est le suicide. Soit on accepte l'absurdité de la vie, soit on choisit de la quitter.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-11T01:49:52.834Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Sinner", "release": {"before_common_era": false, "original": {"__time__": "2023-03-11T01:49:52.834Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "mature": true, "philosophy": true, "psychology": true}, "updated_at": {"__time__": "2023-03-11T01:55:28.440Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-03-11T01:55:28.440Z"}}, "__collections__": {}}, "5LC7XLPVcIIBp3nsGu0i": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-02-02T05:39:54.753Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-02-02T05:39:54.753Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-02T05:39:54.753Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-02-02T05:43:53.227Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "When you know death comes soon, you look around things closer.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-02T05:39:54.753Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Downsizing", "release": {"before_common_era": false, "original": {"__time__": "2023-02-02T05:39:54.753Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "poetry": true, "retrospection": true}, "updated_at": {"__time__": "2023-02-02T05:43:53.227Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-02-02T05:43:53.227Z"}}, "__collections__": {}}, "5XXzPBwJGHEHx4QK1Tn2": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2025-03-08T05:43:06.282999Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2025-03-08T05:43:06.282999Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "categories": {}, "created_at": {"__time__": "2025-03-08T05:45:29.511Z"}, "language": "en", "name": "It’s very hard to work,but when you work,life becomes easy and meaningful.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2025-03-08T05:43:06.282999Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2025-03-08T05:43:06.282999Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "tags": {}, "topics": {}, "updated_at": {"__time__": "2025-03-08T05:45:36.682Z"}, "user": {"id": "mBTpURcXVeYm6OzUBc7oh5QgHQR2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2025-03-08T05:45:29.512Z"}}, "status": "", "updated_at": {"__time__": "2025-03-08T05:45:29.512Z"}}, "__collections__": {}}, "63CzuySCCnA2xsyr58A8": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-02-10T11:26:54.305Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-02-10T11:26:54.305Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-10T11:26:54.305Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-02-10T11:28:56.610Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "L'homme vaut plus que le monde puisqu'il est capable de dominer la nature et de créer des machines pour optimiser son monde.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-10T11:26:54.305Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Les Revues du Monde", "release": {"before_common_era": false, "original": {"__time__": "2023-02-10T11:26:54.305Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"biology": true, "introspection": true, "knowledge": true, "social": true}, "updated_at": {"__time__": "2023-02-10T11:28:56.610Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-02-10T11:28:56.610Z"}}, "__collections__": {}}, "63Dyzxdyto7rrnbhGpbd": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-10-11T00:55:00.096Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-10-11T00:57:52.959Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "One should die proudly when it's no longer possible to live proudly.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Midnight Club", "release": {"before_common_era": false, "original": {"__time__": "2022-10-11T00:55:00.097Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "philosophy": true, "poetry": true}, "updated_at": {"__time__": "2022-10-11T00:57:52.959Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-10-11T00:57:52.959Z"}}, "__collections__": {}}, "6DvF7icUVX8jqnz45pWT": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-28T17:26:58.629Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-28T17:26:58.629Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-28T17:26:58.629Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-05-28T17:29:23.614Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "<PERSON><PERSON><PERSON> qui possède la Lance de La Destinée tient le destin du monde entre ses mains.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-28T17:26:58.629Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Constantine", "release": {"before_common_era": false, "original": {"__time__": "2023-05-28T17:26:58.629Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"metaphor": true, "philosophy": true}, "updated_at": {"__time__": "2023-05-28T17:29:23.614Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-05-28T17:29:23.614Z"}}, "__collections__": {}}, "6OFQwTTk1Na0c37enwLo": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": null}, "death": {"before_common_era": false, "city": "", "country": "", "date": null}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-13T18:05:46.004Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": true, "job": "Student", "name": "<PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-06-13T18:08:52.062Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Think before do anything.....", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-13T18:05:46.004Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": null}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "motivation": true}, "updated_at": {"__time__": "2021-06-13T18:08:52.062Z"}, "user": {"id": "RTpD6ICMShNma4VmYuuMRrHhCLQ2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-06-13T18:08:52.062Z"}}, "__collections__": {}}, "6avdmUEOotjn3mKH4c5u": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-08-03T23:50:21.920Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-03T23:50:21.920Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-08-03T23:55:44.402Z"}, "language": "en", "name": "I Must. I Can. I Will.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-03T23:50:21.920Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-08-03T23:50:21.920Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-08-03T23:55:45.666Z"}, "user": {"id": "iRo6FOKzB4MmOrEUd6wawNZPyuJ2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-08-03T23:55:44.403Z"}}, "status": "", "updated_at": {"__time__": "2024-08-03T23:55:44.503Z"}}, "__collections__": {}}, "7ERTeWwEKJAdeHnW6DL9": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T23:25:38.760Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T23:25:38.760Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "<PERSON><PERSON><PERSON>", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-29T23:27:10.163Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Nobody wants the sherif around when they're drunk.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Things heard and seen", "release": {"before_common_era": false, "original": {"__time__": "2021-05-29T21:59:21.354Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"fun": true}, "updated_at": {"__time__": "2021-05-29T23:27:10.163Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-29T23:27:10.163Z"}}, "__collections__": {}}, "7H0wEbm1zbaKLufPwFUg": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-09-04T18:54:39.081Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Ne faites jamais la veille ce qui peut être fait le demain.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Predestination", "release": {"before_common_era": false, "original": {"__time__": "2021-09-04T18:52:30.707Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"work": true}, "updated_at": {"__time__": "2021-09-04T18:54:39.081Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-09-04T18:54:39.081Z"}}, "__collections__": {}}, "7UTgeQWtCVwJsvmGVP4y": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-25T15:44:30.491Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-25T15:44:30.491Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-25T15:44:30.491Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-25T15:45:33.153Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "C'est la conscience de moi-même en tant que conscience qui perçoit, maintenant.", "reference": {"id": "zknEA6J4kljNQB4LhgP0", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-25T15:45:26.627Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2013-07-17T22:00:00.000Z"}}, "summary": "<PERSON> is the channel of a french YouTuber of the same name, talking about psychology, philosophy, social, among other subjects. He has a videos serie on philosophy where he explains concepts with a modern take.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/cyrusnorth/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyrus North-1610062241274.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/cyrusnorth/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/cyrusnorth", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Cyrus_<PERSON>", "youtube": "https://www.youtube.com/channel/UCah8C0gmLkdtvsy0b2jrjrw"}}, "topics": {"philosophy": true}, "updated_at": {"__time__": "2021-05-25T15:45:33.153Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-25T15:45:33.153Z"}}, "__collections__": {}}, "86GgmhtY1zGBXgyim25s": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-12-08T11:55:02.745Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-12-08T11:55:02.745Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-12-08T11:55:02.745Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-12-08T11:57:05.554Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "The oldest and strongest emotion of mankind is fear, and the oldest and strongest kind of fear is fear of the unknown.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-12-08T11:55:02.745Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Endless", "release": {"before_common_era": false, "original": {"__time__": "2022-12-08T11:55:02.745Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "retrospection": true, "social": true}, "updated_at": {"__time__": "2022-12-08T11:57:05.554Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-12-08T11:57:05.554Z"}}, "__collections__": {}}, "96zyg5BqgsGkuhFc5Vv9": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-23T01:31:52.973Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-23T01:31:52.973Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-23T01:31:52.973Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-03-23T01:33:09.414Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Les grandes révolutions naissent des petites misères comme les grands fleuves des petits ruisseaux.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-23T01:31:52.973Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Blast", "release": {"before_common_era": false, "original": {"__time__": "2023-03-23T01:31:52.973Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"motivation": true, "social": true}, "updated_at": {"__time__": "2023-03-23T01:33:09.414Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-03-23T01:33:09.414Z"}}, "__collections__": {}}, "9JljcsMIBlHTt16aSC07": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-02-16T03:34:03.619Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-02-16T03:34:03.619Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-16T03:34:03.619Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-02-16T03:38:19.046Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "<PERSON><PERSON>me lorsqu'on est opprimé, on ne se libère jamais tout à fait si on ne libére pas son oppresseur. ", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-16T03:34:03.619Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Quand l'histoire fait date", "release": {"before_common_era": false, "original": {"__time__": "2023-02-16T03:34:03.619Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "philosophy": true, "social": true}, "updated_at": {"__time__": "2023-02-16T03:38:19.046Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-02-16T03:38:19.046Z"}}, "__collections__": {}}, "9ZZmNvMAUiLhD8UcYMIG": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-09-01T10:00:23.196Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-09-01T10:00:23.196Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "categories": {}, "created_at": {"__time__": "2024-09-01T10:25:46.375Z"}, "language": "en", "name": "<PERSON>ux qui vivent sont ceux qui luttent.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-09-01T10:00:23.196Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-09-01T10:00:23.196Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "tags": {}, "topics": {}, "updated_at": {"__time__": "2024-09-01T10:25:53.897Z"}, "user": {"id": "2VCWtDjpOSXUM6rq4nZh90rvVEi1"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-09-01T10:25:46.375Z"}}, "status": "", "updated_at": {"__time__": "2024-09-01T10:25:46.375Z"}}, "__collections__": {}}, "AggHcIDaW1yHSKjk8ybf": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-01-30T04:04:52.933Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-01-30T04:04:52.933Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-01-30T04:04:52.933Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-01-30T04:07:19.775Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "If love could have saved you, you would have lived forever.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-01-30T04:04:52.933Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The woman who lived across the street", "release": {"before_common_era": false, "original": {"__time__": "2022-01-30T04:04:52.933Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "poetry": true, "retrospection": true, "spiritual": true}, "updated_at": {"__time__": "2022-01-30T04:07:19.775Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-01-30T04:07:19.775Z"}}, "__collections__": {}}, "BqN5WJsc6S6U67Lcw4dJ": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-07-29T12:34:34.800Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-07-29T12:34:34.800Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-07-29T12:34:34.800Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-07-29T12:44:20.867Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Age is just a number, you can add or subtract, but at the end of the day, when the time comes, you die, anyways.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-07-29T12:34:34.801Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "MADiSON", "release": {"before_common_era": false, "original": {"__time__": "2022-07-06T22:00:00.000Z"}}, "summary": "MADiSON is a first person psychological horror game that delivers an immersive and terrifying experience. With the help of an instant camera, connect the human world with the beyond, take pictures and develop them by yourself.", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "philosophy": true, "psychology": true}, "updated_at": {"__time__": "2022-07-29T12:44:20.867Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-07-29T12:44:20.867Z"}}, "__collections__": {}}, "CYRKGLpTFozVNgpri2Sq": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-07-24T04:50:38.746Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-24T04:50:38.746Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON> of Ockham", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-07-24T04:51:08.414Z"}, "language": "en", "name": "\"It is vain to do with more what can be done with fewer.\"\n", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-24T04:50:38.746Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-07-24T04:50:38.746Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-07-24T04:51:17.082Z"}, "user": {"id": "2Ya4jgspNkNFOWKbhh4gf5a2bys2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-07-24T04:51:08.414Z"}}, "status": "", "updated_at": {"__time__": "2024-07-24T04:51:08.414Z"}}, "__collections__": {}}, "CfNb6ph8KfihgWCZhiOe": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-15T20:40:02.668Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Turns out I had to lose a testicle to become a better man.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Atypical", "release": {"before_common_era": false, "original": {"__time__": "2021-07-15T20:38:58.275Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"biology": true, "fun": true, "mature": true}, "updated_at": {"__time__": "2021-07-15T20:40:02.668Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-15T20:40:02.668Z"}}, "__collections__": {}}, "CjSfLogD51EYldSwYsRx": {"author": {"birth": {"before_common_era": false, "city": "Westland Row, Dublin", "country": "Ireland", "date": {"__time__": "2023-02-10T04:17:36.678Z"}}, "death": {"before_common_era": false, "city": "Paris", "country": "France", "date": {"__time__": "2023-02-10T04:17:36.678Z"}}, "from_reference": {"id": "", "name": ""}, "id": "F3918jwaC9QlGpHC2Rjj", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-10T04:17:36.679Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "Irish poet", "name": "<PERSON>", "summary": "<PERSON><PERSON><PERSON> was an Irish poet and playwright. After writing in different forms throughout the 1880s, the early 1890s saw him become one of the most popular playwrights in London.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOscar Wilde-1610062539926.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON><PERSON>", "youtube": ""}}, "created_at": {"__time__": "2023-02-10T04:20:24.234Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Quand les Dieux veulent nous punir, Ils exaucent nos prières.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-10T04:16:35.426Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Mr. <PERSON>'s Phone", "release": {"before_common_era": false, "original": {"__time__": "2023-02-10T04:16:35.426Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"spiritual": true}, "updated_at": {"__time__": "2023-02-10T04:20:24.234Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-02-10T04:20:24.234Z"}}, "__collections__": {}}, "DMy2URT031xT11TLUN70": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-02-20T21:46:39.996Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-02-20T21:46:39.996Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-02-20T21:46:39.996Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-02-20T21:47:23.750Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Things you own, end up owning you.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-02-20T21:46:39.996Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Fight Club", "release": {"before_common_era": false, "original": {"__time__": "2022-02-20T21:46:39.996Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"psychology": true}, "updated_at": {"__time__": "2022-02-20T21:47:23.750Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-02-20T21:47:23.750Z"}}, "__collections__": {}}, "DQpKbyls80l5gZycreLN": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T12:34:33.435Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T12:34:33.435Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T12:34:33.435Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-31T12:36:21.018Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le vrai peut quelques fois n'être pas vraisemblable.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T12:34:33.435Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "L'art poétique ", "release": {"before_common_era": false, "original": {"__time__": "1674-07-30T22:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "psychology": true}, "updated_at": {"__time__": "2023-07-31T12:36:21.018Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-31T12:36:21.018Z"}}, "__collections__": {}}, "Fu3I6Rx1EvQPtUk8z3hh": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T12:48:13.330Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T12:48:13.330Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T12:48:13.330Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Chapelin", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-26T12:51:10.657Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "L'humanité est composée, non de héros ou de traîtres mais d'hommes et de femmes (…). L'ignorant condamne leurs fautes, mais le sage a de la compassion pour eux.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T12:48:13.331Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "L'opinion publique ", "release": {"before_common_era": false, "original": {"__time__": "2023-07-26T12:48:13.331Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "introspection": true, "social": true}, "updated_at": {"__time__": "2023-07-26T12:51:10.657Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-26T12:51:10.657Z"}}, "__collections__": {}}, "FzyfZE0b9Wx7K5L8ZWZp": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-10-11T01:09:16.269Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-10-11T01:09:16.269Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-10-11T01:15:00.156Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le secret du monde, c'est que toute chose subsiste et ne meurt pas, mais se soustrait à notre regard, pour mieux revenir ensuite.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Midnight Club", "release": {"before_common_era": false, "original": {"__time__": "2022-10-11T00:55:00.097Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"knowledge": true, "poetry": true, "spiritual": true}, "updated_at": {"__time__": "2022-10-11T01:15:00.156Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-10-11T01:15:00.156Z"}}, "__collections__": {}}, "G3GOjJLxfsQFWEtPQQic": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-06-25T14:02:39.736Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-06-25T14:02:39.736Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-25T14:02:39.736Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-06-25T14:04:18.882Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le monde ne mourra jamais par manque de merveilles mais uniquement par manque d'émerveillement.", "reference": {"id": "yTLZPjfPDD07AD9ZUq2O", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-25T14:04:11.228Z"}, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2021-06-25T14:04:11.228Z"}}, "summary": "Balade Mentale is a french YouTube channel exploring the Universe in poetry. It explores topics like the ending of the Universe, stars' lifespan, or the Library of Babel.", "urls": {"amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AATXAJyIaTK7HHmPeyfI35MF7EEG3yrMw43vazxx8A=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/channel/UCS_7tplUgzJG4DhA16re5Yg/featured", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "gratitude": true, "social": true}, "updated_at": {"__time__": "2021-06-25T14:04:18.882Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-06-25T14:04:18.882Z"}}, "__collections__": {}}, "G7KScOms34r3XSbTqC5y": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-06-24T13:49:55.541Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-06-24T13:49:55.541Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-24T13:49:55.541Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-06-24T13:50:48.649Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "L'essentiel est invisible pour les yeux.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-24T13:49:55.541Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Le Petit Prince", "release": {"before_common_era": false, "original": {"__time__": "2021-06-24T13:49:55.541Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"biology": true, "poetry": true}, "updated_at": {"__time__": "2021-06-24T13:50:48.649Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-06-24T13:50:48.649Z"}}, "__collections__": {}}, "GM0SaExDPBHYxewIK6bG": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-18T05:25:09.665Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-18T05:25:09.665Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-18T05:25:09.665Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-01-18T05:25:50.564Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "You reap what you woe.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-18T05:25:09.665Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Wednesday ", "release": {"before_common_era": false, "original": {"__time__": "2023-01-18T05:25:09.665Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"proverb": true}, "updated_at": {"__time__": "2023-01-18T05:25:50.564Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-01-18T05:25:50.564Z"}}, "__collections__": {}}, "GsdNDVwuqT3o8HvRc1DT": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-09-04T18:55:35.465Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Si vous réussissez au moins une fois, n'essayez plus jamais.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Predestination", "release": {"before_common_era": false, "original": {"__time__": "2021-09-04T18:52:30.707Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"work": true}, "updated_at": {"__time__": "2021-09-04T18:55:35.465Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-09-04T18:55:35.465Z"}}, "__collections__": {}}, "GvDTV1XQ1HNZyLw4iNMP": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Aristote ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-26T13:23:22.316Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le plus important est l'agencement des faits en système.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "La poétique ", "release": {"before_common_era": false, "original": {"__time__": "1852-07-25T22:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"poetry": true, "work": true}, "updated_at": {"__time__": "2023-07-26T13:23:22.316Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-26T13:23:22.316Z"}}, "__collections__": {}}, "GvNhBVxOCSNWC96qcY03": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-10-11T01:09:16.269Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-10-11T01:09:16.269Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-10-11T01:12:58.972Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "It is the secret of the world that all things subsist and do not die, but retire a little from the sight, and then afterwards, return again.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Midnight Club", "release": {"before_common_era": false, "original": {"__time__": "2022-10-11T00:55:00.097Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"knowledge": true, "poetry": true, "spiritual": true}, "updated_at": {"__time__": "2022-10-11T01:12:58.972Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-10-11T01:12:58.972Z"}}, "__collections__": {}}, "HMKUUIkp50aCkWcOoNOg": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-06-13T12:12:43.417999Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-13T12:12:43.417999Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-06-13T12:14:03.909Z"}, "language": "en", "name": "C'est bien la pire peine,\nDe ne savoir pourquoi,\nSans amour et sans haine,\nMon cœur a tant de peine !", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-13T12:12:43.417999Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON>, Romance sans paroles", "release": {"before_common_era": false, "original": {"__time__": "2024-06-13T12:12:43.417999Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-06-13T12:14:12.000Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-06-13T12:14:03.910Z"}}, "status": "", "updated_at": {"__time__": "2024-06-13T12:14:03.910Z"}}, "__collections__": {}}, "HeNGacdRDXT0HP9FEfqX": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-06-10T08:16:13.275Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-06-10T08:16:13.276Z"}}, "from_reference": {"id": "", "name": ""}, "id": "pBlXzmHt8hyNRDZY6qdC", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-10T08:16:13.276Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "French content producer", "name": "<PERSON>", "summary": "<PERSON> is a french content producer specialized in history and archeology. She has a YouTube channel, Les Revues du Monde, where she tells stories on various subjects including ancient unresolved cases, critical expeditions, or myths. Her videos often gives a good look on the past. She also explains the work of an archeologist.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/revuesdumonde", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCharlie Danger-1610238225804.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/charlie__danger/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/revuesdumonde", "website": "https://en.tipeee.com/les-revues-du-monde", "wikipedia": "https://fr.wikipedia.org/wiki/<PERSON>_<PERSON>", "youtube": "https://www.youtube.com/channel/UCnf0fDz1vTYW-sl36wbVMbg"}}, "created_at": {"__time__": "2021-06-10T08:16:43.551Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Nous n'avons que notre art pour représenter celui des autres.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-10T08:15:32.963Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Les revues du monde", "release": {"before_common_era": false, "original": {"__time__": "2021-06-10T08:15:32.963Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"art": true, "social": true}, "updated_at": {"__time__": "2021-06-10T08:16:43.551Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-06-10T08:16:43.551Z"}}, "__collections__": {}}, "HwbkJLTPYrKdCfDQsp2K": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-11T23:34:14.673Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-11T23:34:14.673Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-11T23:22:47.673Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "Psychologist", "name": "<PERSON>", "summary": "ep 5", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-11T23:34:42.771Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "The world has always been chaos and shit, my friend.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-11T23:22:47.673Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Jupiter's Legacy", "release": {"before_common_era": false, "original": {"__time__": "2021-05-11T23:22:47.673Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"philosophy": true}, "updated_at": {"__time__": "2021-05-11T23:34:42.771Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-11T23:34:42.771Z"}}, "__collections__": {}}, "IYmMYKALY6O9LkSxvQ8u": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-08-06T04:05:35.245Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-08-06T04:05:35.245Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-08-06T04:05:35.245Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-08-06T04:06:48.873Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "<PERSON>ow momey", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-08-06T04:05:35.245Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2021-08-06T04:05:35.245Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"fun": true, "poetry": true, "punchline": true, "retrospection": true, "social": true}, "updated_at": {"__time__": "2021-08-06T04:06:48.873Z"}, "user": {"id": "PBVkxeYtCucHotrptw7skTqb3vx2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-08-06T04:06:48.873Z"}}, "__collections__": {}}, "J0VmlDcSvUcyDVrvs8Jl": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-08-01T15:32:43.259Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-08-01T15:32:43.259Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-01T15:32:43.259Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-08-01T15:33:30.702Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Ce que l'on conçoit bien s'énonce clairement.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-01T15:33:20.307Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "L'art poétique ", "release": {"before_common_era": false, "original": {"__time__": "2023-08-01T15:33:20.307Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"language": true, "work": true}, "updated_at": {"__time__": "2023-08-01T15:33:30.702Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-08-01T15:33:30.702Z"}}, "__collections__": {}}, "JNGFicTYI9DPEUBotF2U": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-08-27T08:45:44.016Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-27T08:45:44.016Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": " <PERSON><PERSON><PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "categories": {}, "created_at": {"__time__": "2024-08-27T08:46:20.055Z"}, "language": "en", "name": "\"The art of life is a constant readjustment to our surroundings.\"\n", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-27T08:45:44.016Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-08-27T08:45:44.016Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "tags": {}, "topics": {}, "updated_at": {"__time__": "2024-08-27T08:46:25.905Z"}, "user": {"id": "7Binj8yNatVki43rPX7RfYAhMKA2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-08-27T08:46:20.055Z"}}, "status": "", "updated_at": {"__time__": "2024-08-27T08:46:20.055Z"}}, "__collections__": {}}, "JjFNFzbIY5XqQkcyEY7Z": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-15T21:00:20.129Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Mais ce n'est pas parce qu'on fait une pause dans notre couple que je mets mon amour pour toi en pause.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Atypical", "release": {"before_common_era": false, "original": {"__time__": "2021-07-15T20:38:58.275Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true}, "updated_at": {"__time__": "2021-07-15T21:00:20.129Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-15T21:00:20.129Z"}}, "__collections__": {}}, "KMXJIwuQdilXMVZWFiDg": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-09-04T18:53:29.760Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Never do yesterday what should be done tomorrow.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Predestination", "release": {"before_common_era": false, "original": {"__time__": "2021-09-04T18:52:30.707Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"work": true}, "updated_at": {"__time__": "2021-09-04T18:53:29.760Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-09-04T18:53:29.760Z"}}, "__collections__": {}}, "KiIBRbui9MqkMzPNQLdc": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-22T04:00:55.537Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-22T04:00:55.537Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-22T04:00:55.537Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-03-22T04:03:42.162Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Je veux mourir dans mon sommeil comme mon grand-père, pas en hurlant de terreur comme ses passagers.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-22T04:00:55.537Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Don't Look Up", "release": {"before_common_era": false, "original": {"__time__": "2023-03-22T04:00:55.537Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "spiritual": true, "travel": true}, "updated_at": {"__time__": "2023-03-22T04:03:42.162Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-03-22T04:03:42.162Z"}}, "__collections__": {}}, "KnpSCZ1N3GIXdZttFX0l": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-21T02:14:10.534Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-21T02:14:10.534Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-21T02:14:10.534Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Vi", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-11-21T02:20:29.564Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "<PERSON> sais, c'est ce qui te rend différente qui te rend forte.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-21T02:14:10.535Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2021-11-21T02:14:10.535Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"psychology": true}, "updated_at": {"__time__": "2021-11-21T02:20:29.564Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-11-21T02:20:29.564Z"}}, "__collections__": {}}, "L6ODHCboo7dbTbrfN7tt": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-10-11T01:09:16.269Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-10-11T01:09:16.269Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-10-11T01:16:55.494Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "<PERSON><PERSON><PERSON>, c'est descendre d'une voiture pour monter dans une autre.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Midnight Club", "release": {"before_common_era": false, "original": {"__time__": "2022-10-11T00:55:00.097Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"metaphor": true}, "updated_at": {"__time__": "2022-10-11T01:16:55.494Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-10-11T01:16:55.494Z"}}, "__collections__": {}}, "LYeERgEK2C0szlbFdKby": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2025-01-06T01:09:44.301Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2025-01-06T01:09:44.301Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2025-01-06T01:11:59.200Z"}, "language": "en", "name": "It's funny how sometimes the people we remember the least make the greatest impression on us.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2025-01-06T01:09:44.301Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Curious Case of <PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2025-01-06T01:09:44.301Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2025-01-06T01:12:05.898Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2025-01-06T01:11:59.201Z"}}, "status": "", "updated_at": {"__time__": "2025-01-06T01:11:59.201Z"}}, "__collections__": {}}, "LyTguecc8USOyP7RtuDo": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-15T06:16:01.485Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-15T06:16:01.485Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-15T06:16:01.485Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-01-15T06:18:29.841Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Une coïncidence est juste une coïncidence, deux sont un indice, trois sont une preuve.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-15T06:16:01.485Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Wednesday ", "release": {"before_common_era": false, "original": {"__time__": "2023-01-15T06:16:01.485Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"psychology": true, "work": true}, "updated_at": {"__time__": "2023-01-15T06:18:29.841Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-01-15T06:18:29.841Z"}}, "__collections__": {}}, "M7S42esgXTY1OcFsGQFt": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-01T03:48:04.077Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-01T03:48:04.077Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-01T03:48:04.077Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-09-01T03:49:22.773Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Qu'est-ce que le bonheur sinon le simple accord entre un être et l'existence qu'il mène ?", "reference": {"id": "Yj5cEusYhyvzqCyFofGZ", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-01T03:49:11.126Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "OkCupid", "release": {"before_common_era": false, "original": {"__time__": "2021-09-01T03:49:11.126Z"}}, "summary": "OkCupid is an American-based, internationally operating online dating, friendship, and social networking website that features multiple-choice questions in order to match members. It is supported by advertisements, by paying users who do not see ads, and by selling user data for data mining.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fokcupid.png?alt=media&token=a8bef069-60b7-4b77-8e9d-9eebb5e821f0", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.okcupid.com/", "wikipedia": "https://en.wikipedia.org/wiki/OkCupid", "youtube": ""}}, "topics": {"feelings": true, "introspection": true, "philosophy": true}, "updated_at": {"__time__": "2021-09-01T03:49:22.773Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-09-01T03:49:22.773Z"}}, "__collections__": {}}, "MKhzSZD3NZnvsACo771h": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-11-22T23:42:14.534Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "It's a sad truth that those who shine brightest often burn fastest.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2021-11-22T23:34:15.626Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"knowledge": true, "poetry": true, "retrospection": true, "work": true}, "updated_at": {"__time__": "2021-11-22T23:42:14.534Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-11-22T23:42:14.534Z"}}, "__collections__": {}}, "NWGnbql9kh9XWFyYkprD": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-27T14:30:47.081Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-27T14:30:47.081Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-27T14:30:47.081Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-27T14:32:56.280Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "C'est sur le chemin où l'on croit l'éviter que souvent l'on rencontre son destin", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-27T14:30:47.081Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Kung fu panda", "release": {"before_common_era": false, "original": {"__time__": "2023-07-27T14:30:47.081Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "philosophy": true, "psychology": true, "travel": true}, "updated_at": {"__time__": "2023-07-27T14:32:56.280Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-27T14:32:56.280Z"}}, "__collections__": {}}, "NhwvnO37OdIA81yjeYlB": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-28T17:26:58.629Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-28T17:26:58.629Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-28T17:26:58.629Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-05-28T17:28:23.389Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "He who possesses the Spear of <PERSON> holds the fate of the world in his hands.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-28T17:26:58.629Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Constantine", "release": {"before_common_era": false, "original": {"__time__": "2023-05-28T17:26:58.629Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"metaphor": true, "philosophy": true}, "updated_at": {"__time__": "2023-05-28T17:28:23.389Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-05-28T17:28:23.389Z"}}, "__collections__": {}}, "NsX5UYy9KEe16vkNXCMj": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-08-03T23:50:48.459Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-03T23:50:48.459Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-08-03T23:55:39.234Z"}, "language": "en", "name": "Roll Call🔊: future entrepreneurs, future lawyers, future winners 🫡", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-03T23:50:48.459Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-08-03T23:50:48.459Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-08-03T23:55:43.240Z"}, "user": {"id": "iRo6FOKzB4MmOrEUd6wawNZPyuJ2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-08-03T23:55:39.234Z"}}, "status": "", "updated_at": {"__time__": "2024-08-03T23:55:39.234Z"}}, "__collections__": {}}, "OWZdiK3VHErYTchgvk9V": {"author": {"birth": {"before_common_era": false, "city": "Alençon, Normandy", "country": "France", "date": {"__time__": "2021-10-23T22:00:17.240Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-10-23T22:00:17.240Z"}}, "from_reference": {"id": "", "name": ""}, "id": "tfcJHq7qiyPsN0EZMqUP", "image": {"credits": {"artist": "<PERSON>", "before_common_era": false, "company": "", "date": {"__time__": "2018-12-31T23:00:00.000Z"}, "location": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON> 2019", "url": "https://en.wikipedia.org/wiki/Orelsan?oldformat=true#/media/File:Or<PERSON><PERSON>_Deauville_2019.jpg"}}, "is_fictional": false, "job": "French rapper", "name": "<PERSON><PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON><PERSON>, better known by his stage name <PERSON><PERSON><PERSON>, sometimes stylized as <PERSON><PERSON><PERSON><PERSON>, is a French rapper, songwriter, record producer, actor and film director.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/orelsan", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOrelsan-1619990138849.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/orelsan/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/Orel_San", "website": "https://orelsan.tv/music/", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON><PERSON>an", "youtube": "https://www.youtube.com/channel/UCEnFzIYw3BrndPCddyQ6c5A"}}, "created_at": {"__time__": "2021-10-23T22:01:45.836Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "L'avenir appartient à ceux qui se lèvent à l'heure où je me couche.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-10-23T21:59:42.989Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "À l'heure où j'me couche", "release": {"before_common_era": false, "original": {"__time__": "2014-12-31T23:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"philosophy": true}, "updated_at": {"__time__": "2021-10-23T22:01:45.836Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-10-23T22:01:45.836Z"}}, "__collections__": {}}, "OqX8hbOTzsF4rTXwVeCE": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-08-26T08:30:00.105Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-26T08:30:00.105Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON><PERSON><PERSON><PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "categories": {}, "created_at": {"__time__": "2024-08-26T08:31:22.701Z"}, "language": "en", "name": "\"Nothing is permanent but change.\" ", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-26T08:30:00.105Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-08-26T08:30:00.105Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "tags": {}, "topics": {}, "updated_at": {"__time__": "2024-08-26T08:31:28.574Z"}, "user": {"id": "7Binj8yNatVki43rPX7RfYAhMKA2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-08-26T08:31:22.701Z"}}, "status": "", "updated_at": {"__time__": "2024-08-26T08:31:22.701Z"}}, "__collections__": {}}, "P9kse47zPWfCWAGIRzUG": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-08-03T23:49:57.565Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-03T23:49:57.565Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-08-03T23:55:28.783Z"}, "language": "en", "name": "Let your Why be deeper than You. ", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-03T23:49:57.565Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-08-03T23:49:57.565Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-08-03T23:55:35.428Z"}, "user": {"id": "iRo6FOKzB4MmOrEUd6wawNZPyuJ2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-08-03T23:55:28.783Z"}}, "status": "", "updated_at": {"__time__": "2024-08-03T23:55:28.784Z"}}, "__collections__": {}}, "PHg2lqHWxb9Z1SX2Yc92": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-29T08:08:33.041Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-29T08:08:33.041Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-29T08:08:33.041Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-29T08:09:22.177Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "The plural of anecdote is not data.", "reference": {"id": "qMG13EMdHApbx9zXvrAd", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-29T08:09:13.031Z"}, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Dirty Biology", "release": {"before_common_era": false, "original": {"__time__": "2021-07-29T08:09:13.031Z"}}, "summary": "Sciences videos on mindfuck subject, dirty or just fun. Sometimes we talk about biology too.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDirty%20Biology.jpg?alt=media&token=9ab3b79e-ecca-4a20-8793-2b1eb8435b76", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/user/dirtybiology", "wikipedia": "", "youtube": ""}}, "topics": {"sciences": true}, "updated_at": {"__time__": "2021-07-29T08:09:22.177Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-29T08:09:22.177Z"}}, "__collections__": {}}, "PY0GfKQAa8KSkqHRpJ0S": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-08-04T19:32:46.889Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-08-04T19:32:46.889Z"}}, "from_reference": {"id": "z2gmE4AfOjaZbnBirVvE", "name": ""}, "id": "YKQGg4TbHX4m7XI8smhd", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-08-04T19:32:46.889Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": true, "job": "<PERSON>", "name": "<PERSON><PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON> is a fictional character played by <PERSON><PERSON> in the TV series \"<PERSON><PERSON><PERSON> (The Gift)\". In the show, she's a painter who has strange visions and is fascinated with a particular symbol. She will start a travel in search of the truth.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAtiye-1604423792750.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>", "youtube": ""}}, "created_at": {"__time__": "2021-08-04T19:33:39.270Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le temps est l'infinité du moment.", "reference": {"id": "z2gmE4AfOjaZbnBirVvE", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-08-04T19:32:59.079Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Gift (Atiye)", "release": {"before_common_era": false, "original": {"__time__": "2019-12-26T23:00:00.000Z"}}, "summary": "The Gift (known in Turkey as At<PERSON><PERSON>) is a Turkish psychological thriller Netflix series starring <PERSON><PERSON>. It was written by <PERSON> and <PERSON><PERSON><PERSON>. The first season consists of 8 episodes and became available for streaming on Netflix on December 27, 2019.[1][2] The series is an adaptation of the novel <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON>. The show tells the story of a painter in Istanbul embarks on a personal journey as she unearths universal secrets about an Anatolian archaeological site and its link to her past.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Gift (Atiye)-1604423035059.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/81037848", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Gift_(Turkish_TV_series)", "youtube": ""}}, "topics": {"philosophy": true, "poetry": true}, "updated_at": {"__time__": "2021-08-04T19:33:39.270Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-08-04T19:33:39.270Z"}}, "__collections__": {}}, "PjtHUOGMr4E2Hhv2GJ3z": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-08-24T10:54:20.006999Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-24T10:54:20.006999Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "Socrates", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "categories": {}, "created_at": {"__time__": "2024-08-27T10:24:25.225Z"}, "language": "en", "name": "The secret of happiness, you see, is not found in seeking more, but in developing the capacity to enjoy less. ", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-24T10:54:20.006999Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-08-24T10:54:20.006999Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "tags": {}, "topics": {}, "updated_at": {"__time__": "2024-08-27T10:24:31.300Z"}, "user": {"id": "7Binj8yNatVki43rPX7RfYAhMKA2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-08-27T10:24:25.225Z"}}, "status": "", "updated_at": {"__time__": "2024-08-27T10:24:25.225Z"}}, "__collections__": {}}, "Q1HqaRrzR71yuc99rx5j": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-11-12T14:53:23.438Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-11-12T14:53:23.438Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-11-12T14:53:23.438Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-11-12T14:59:40.251Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Il vaut mieux ne pas rêver à Fishtown. Le réveil est trop douloureux.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-11-12T14:53:23.438Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Dispatch from Elsewhere", "release": {"before_common_era": false, "original": {"__time__": "2022-11-12T14:53:23.438Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "introspection": true, "poetry": true}, "updated_at": {"__time__": "2022-11-12T14:59:40.251Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-11-12T14:59:40.251Z"}}, "__collections__": {}}, "Q4lWNt0cpNgOYM9dLb1u": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-13T23:40:11.333Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-13T23:40:11.333Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-13T23:40:11.333Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-03-13T23:42:57.324Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "La vie n'est que le naufrage de nos plans.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-13T23:40:11.333Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Shape of Water", "release": {"before_common_era": false, "original": {"__time__": "2023-03-13T23:40:11.333Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"metaphor": true, "poetry": true, "travel": true}, "updated_at": {"__time__": "2023-03-13T23:42:57.324Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-03-13T23:42:57.324Z"}}, "__collections__": {}}, "QTdceGoBhLYdEXHLNJcP": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-25T16:16:48.558Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-25T16:16:48.558Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-25T16:16:48.558Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": true, "job": "", "name": "<PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-03-25T16:19:02.035Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "History lives in us, whether we learn it or not.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-25T16:16:48.558Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2023-03-25T16:16:48.558Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "knowledge": true, "travel": true}, "updated_at": {"__time__": "2023-03-25T16:19:02.035Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-03-25T16:19:02.035Z"}}, "__collections__": {}}, "Qivg4HWbsLEOOgrARSU1": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-28T22:47:58.304Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-28T22:47:58.304Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-28T22:47:58.304Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-01-28T22:49:33.861Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "L'important, ce n'est pas la chute. C'est l'atterrissage.", "reference": {"id": "Yj5cEusYhyvzqCyFofGZ", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-28T22:49:18.745Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "OkCupid", "release": {"before_common_era": false, "original": {"__time__": "2023-01-28T22:49:18.745Z"}}, "summary": "OkCupid is an American-based, internationally operating online dating, friendship, and social networking website that features multiple-choice questions in order to match members. It is supported by advertisements, by paying users who do not see ads, and by selling user data for data mining.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fokcupid.png?alt=media&token=a8bef069-60b7-4b77-8e9d-9eebb5e821f0", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.okcupid.com/", "wikipedia": "https://en.wikipedia.org/wiki/OkCupid", "youtube": ""}}, "topics": {"metaphor": true, "motivation": true, "psychology": true, "travel": true}, "updated_at": {"__time__": "2023-01-28T22:49:33.861Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-01-28T22:49:33.861Z"}}, "__collections__": {}}, "QxIUywFuVIOzSuLChR7r": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-15T20:58:50.229Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Just because we're taking a break from being girlfriend and boyfriend doesn't mean I'll ever take a break from being in love with you.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Atypical", "release": {"before_common_era": false, "original": {"__time__": "2021-07-15T20:38:58.275Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true}, "updated_at": {"__time__": "2021-07-15T20:58:50.229Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-15T20:58:50.229Z"}}, "__collections__": {}}, "RNEoftVDkfrnUBuSfiN6": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T23:39:40.408Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T23:39:40.408Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "<PERSON><PERSON><PERSON>", "name": "Local woman", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-29T23:41:27.827Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "La bonté triomphe toujours. Si ce n'est dans ce monde, dans le suivant.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Things heard and seen", "release": {"before_common_era": false, "original": {"__time__": "2021-05-29T21:59:21.354Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "spiritual": true}, "updated_at": {"__time__": "2021-05-29T23:41:27.827Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-29T23:41:27.827Z"}}, "__collections__": {}}, "Rjrwj34GPnAUfd3MIEwi": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T12:23:57.828Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T12:23:57.828Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T12:23:57.828Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON><PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-26T12:26:33.521Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Couvrez ce sein que je ne saurais voir.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T12:23:57.829Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Le Tartuffe", "release": {"before_common_era": false, "original": {"__time__": "2023-07-26T12:23:57.829Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "fun": true, "social": true}, "updated_at": {"__time__": "2023-07-26T12:26:33.521Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-26T12:26:33.521Z"}}, "__collections__": {}}, "RrCru7NxT2tYuRrGctwW": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-08-29T06:50:34.937999Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-29T06:50:34.937999Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": " <PERSON><PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "categories": {}, "created_at": {"__time__": "2024-08-29T06:50:59.777Z"}, "language": "en", "name": "\"Let come what comes, let go what goes. See what remains.\"\n", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-29T06:50:34.937999Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-08-29T06:50:34.937999Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "tags": {}, "topics": {}, "updated_at": {"__time__": "2024-08-29T06:51:06.439Z"}, "user": {"id": "7Binj8yNatVki43rPX7RfYAhMKA2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-08-29T06:50:59.777Z"}}, "status": "", "updated_at": {"__time__": "2024-08-29T06:50:59.777Z"}}, "__collections__": {}}, "S8i12xh3NB79lWc3mS9b": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-03T13:34:30.250Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-03T13:34:30.250Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-03T13:34:30.250Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-03T13:37:04.191Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le problème, lorsqu'on se venge par jalousie, c'est que même si l'on inflige de la douleur aux autres, cela n'atténue en rien celle que l'on ressent soi-même. Au bout du compte, tout le monde est malheureux.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-03T13:34:30.250Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "L'île des Chasseurs d'oiseaux ", "release": {"before_common_era": false, "original": {"__time__": "2023-07-03T13:34:30.250Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true}, "updated_at": {"__time__": "2023-07-03T13:37:04.191Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-03T13:37:04.191Z"}}, "__collections__": {}}, "SPdNaAInsyCGu8iN6kLy": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-08T08:33:37.023Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-08T08:33:37.023Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-08T08:33:37.023Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-08T08:34:30.134Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "L'éternité c'est long, surtout vers la fin.", "reference": {"id": "yTLZPjfPDD07AD9ZUq2O", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-08T08:34:21.430Z"}, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2021-07-08T08:34:21.430Z"}}, "summary": "Balade Mentale is a french YouTube channel exploring the Universe in poetry. It explores topics like the ending of the Universe, stars' lifespan, or the Library of Babel.", "urls": {"amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AATXAJyIaTK7HHmPeyfI35MF7EEG3yrMw43vazxx8A=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/channel/UCS_7tplUgzJG4DhA16re5Yg/featured", "wikipedia": "", "youtube": ""}}, "topics": {"poetry": true}, "updated_at": {"__time__": "2021-07-08T08:34:30.134Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-08T08:34:30.134Z"}}, "__collections__": {}}, "SmvDA6JibuCBjY9zrF1n": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-26T13:20:04.706Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Les perles ne font pas le collier, c'est le fil.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Correspondances", "release": {"before_common_era": false, "original": {"__time__": "1852-07-25T22:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"fun": true}, "updated_at": {"__time__": "2023-07-26T13:20:04.706Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-26T13:20:04.706Z"}}, "__collections__": {}}, "SqUGxCSJ86Xyw5QZFI4t": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-05T15:45:25.150Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-05T15:45:25.150Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-05T15:45:25.150Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-05-05T15:46:26.984Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "La science ne brise pas la magie, elle la revele.", "reference": {"id": "yTLZPjfPDD07AD9ZUq2O", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-05T15:46:19.273Z"}, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2023-05-05T15:46:19.273Z"}}, "summary": "Balade Mentale is a french YouTube channel exploring the Universe in poetry. It explores topics like the ending of the Universe, stars' lifespan, or the Library of Babel.", "urls": {"amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AATXAJyIaTK7HHmPeyfI35MF7EEG3yrMw43vazxx8A=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/channel/UCS_7tplUgzJG4DhA16re5Yg/featured", "wikipedia": "", "youtube": ""}}, "topics": {"poetry": true, "sciences": true}, "updated_at": {"__time__": "2023-05-05T15:46:26.984Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-05-05T15:46:26.984Z"}}, "__collections__": {}}, "T7t1JX0qcTlDX3wnU1Wb": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-16T02:34:55.078Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-16T02:34:55.078Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-16T02:34:55.078Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-16T02:40:17.027Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le savoir ne réside pas seulement dans les livres, les laboratoires, les fiches mais dans et par l'amitié, celles des hommes, celle des bêtes et des étoiles.", "reference": {"id": "Yj5cEusYhyvzqCyFofGZ", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-16T02:35:57.260Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "OkCupid", "release": {"before_common_era": false, "original": {"__time__": "2021-05-16T02:35:57.260Z"}}, "summary": "OkCupid is an American-based, internationally operating online dating, friendship, and social networking website that features multiple-choice questions in order to match members. It is supported by advertisements, by paying users who do not see ads, and by selling user data for data mining.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fokcupid.png?alt=media&token=a8bef069-60b7-4b77-8e9d-9eebb5e821f0", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.okcupid.com/", "wikipedia": "https://en.wikipedia.org/wiki/OkCupid", "youtube": ""}}, "topics": {"biology": true, "feelings": true, "knowledge": true, "social": true}, "updated_at": {"__time__": "2021-05-16T02:40:17.027Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-16T02:40:17.027Z"}}, "__collections__": {}}, "TQvHVyoijY2LfPYN9PBn": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-27T23:10:19.921Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-27T23:10:19.921Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-27T23:10:19.921Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-27T23:13:01.470Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Until you bring what's unconscious into consciousness, it will control your life. And you'll call it fate.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-27T23:10:19.921Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Sinner", "release": {"before_common_era": false, "original": {"__time__": "2021-07-27T23:10:19.921Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "philosophy": true, "retrospection": true}, "updated_at": {"__time__": "2021-07-27T23:13:01.470Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-27T23:13:01.470Z"}}, "__collections__": {}}, "USDHCZbaXea7voqqeOyj": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": null}, "death": {"before_common_era": false, "city": "", "country": "", "date": null}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-15T18:07:19.096Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-05-15T18:08:45.314Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Hello Flutter", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-15T18:07:19.096Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": null}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"work": true}, "updated_at": {"__time__": "2023-05-15T18:08:45.314Z"}, "user": {"id": "eyWZOTOxm0ex8HBhLFUTed752wG2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-05-15T18:08:45.314Z"}}, "__collections__": {}}, "UYUp0LICl7TMGth59XbK": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-29T00:50:29.390Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-29T00:50:29.390Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-29T00:50:29.390Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Erin", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-09-29T00:51:38.343Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "We are the cosmos dreaming of itself.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-29T00:50:29.390Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Midnight Mass", "release": {"before_common_era": false, "original": {"__time__": "2021-09-29T00:50:29.390Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"poetry": true, "sciences": true}, "updated_at": {"__time__": "2021-09-29T00:51:38.343Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-09-29T00:51:38.343Z"}}, "__collections__": {}}, "Uf9r1s3scpFBz1xr6gGz": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-15T06:19:09.857Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-15T06:19:09.857Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-15T06:16:01.485Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-01-15T06:19:28.455Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "One coincidence is just a coincidence, two are a clue, three are proof.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-15T06:16:01.485Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Wednesday ", "release": {"before_common_era": false, "original": {"__time__": "2023-01-15T06:16:01.485Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"psychology": true, "work": true}, "updated_at": {"__time__": "2023-01-15T06:19:28.455Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-01-15T06:19:28.455Z"}}, "__collections__": {}}, "V1zCqI3tomuRFzwYbHKw": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-27T12:46:37.247Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-27T12:46:37.247Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-27T12:46:37.247Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-11-27T12:49:20.195Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "L'homme n'est rien d'autre que ce qu'il se fait.", "reference": {"id": "zknEA6J4kljNQB4LhgP0", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-27T12:47:41.308Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2013-07-17T22:00:00.000Z"}}, "summary": "<PERSON> is the channel of a french YouTuber of the same name, talking about psychology, philosophy, social, among other subjects. He has a videos serie on philosophy where he explains concepts with a modern take.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/cyrusnorth/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyrus North-1610062241274.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/cyrusnorth/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/cyrusnorth", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Cyrus_<PERSON>", "youtube": "https://www.youtube.com/channel/UCah8C0gmLkdtvsy0b2jrjrw"}}, "topics": {"philosophy": true}, "updated_at": {"__time__": "2021-11-27T12:49:20.195Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-11-27T12:49:20.195Z"}}, "__collections__": {}}, "V3P8YhGir0A56yFuWPtQ": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T23:41:32.122Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T23:41:32.122Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "<PERSON><PERSON><PERSON>", "name": "Local woman", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-29T23:42:22.312Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Goodness always triumphs. Always. If not in this world, then in the next.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Things heard and seen", "release": {"before_common_era": false, "original": {"__time__": "2021-05-29T21:59:21.354Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "spiritual": true}, "updated_at": {"__time__": "2021-05-29T23:42:22.312Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-29T23:42:22.312Z"}}, "__collections__": {}}, "V83E6k4dK19oNY4xTSr8": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-06-09T18:50:34.305Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-09T18:50:34.305Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-06-09T18:52:47.114Z"}, "language": "en", "name": "La victoire appartient au plus opiniâtre ", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-09T18:50:34.305Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-06-09T18:50:34.305Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-06-09T18:52:55.164Z"}, "user": {"id": "p4PWrK73GUWam8wxaOmE64zKFR52"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-06-09T18:52:47.114Z"}}, "status": "", "updated_at": {"__time__": "2024-06-09T18:52:47.115Z"}}, "__collections__": {}}, "VF848Y3Nd3fsSxNKlZqA": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "1980-04-28T22:00:00.000Z"}}, "from_reference": {"id": ""}, "id": "JpDYE0xycqDBUjZYJD9i", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-09T14:17:29.000Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "Film director and screenwriter", "job_localized": {}, "name": "<PERSON>", "summary": "Sir <PERSON> was an English film director, screenwriter, producer and editor. He is widely regarded as one of the most influential figures in the history of cinema. In a career spanning six decades, he directed over 50 feature films, many of which are still widely watched and studied today.", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAlfred <PERSON>-1702425521914.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>", "youtube": ""}}, "created_at": {"__time__": "2023-07-31T13:44:31.319Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Il est indispensable que le public soit parfaitement informé des éléments en jeu.", "reference": {"id": "7naEZTo8VCBpjwg9wt9o", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-08-09T14:17:45.000Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "1966-08-08T23:00:00.000Z"}}, "summary": "«Entretiens» is a book based on a serie of audio recordings between <PERSON> and <PERSON> on a 8-days period. In the 30 hours of conversation, <PERSON> and <PERSON><PERSON><PERSON> discussed the whole work of the film director. The book is probably one of the most famous reference in cinema.", "type": {"primary": "book", "secondary": "Interview"}, "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FEntretiens-1702425521373.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"knowledge": true, "social": true, "work": true}, "updated_at": {"__time__": "2024-01-19T17:06:15.900Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-01-19T17:06:05.139Z"}}, "status": "", "updated_at": {"__time__": "2023-07-31T13:44:31.319Z"}}, "__collections__": {}}, "VOJ4wtGgTO4j0g2eygNA": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-11T23:22:47.673Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-11T23:22:47.673Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-11T23:22:47.673Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Psy", "summary": "ep 5", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-11T23:27:35.721Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Reality always wins out, no matter how profoundly we rail against it.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-11T23:22:47.673Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Jupiter's Legacy", "release": {"before_common_era": false, "original": {"__time__": "2021-05-11T23:22:47.673Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "philosophy": true, "psychology": true}, "updated_at": {"__time__": "2021-05-11T23:27:35.721Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-11T23:27:35.721Z"}}, "__collections__": {}}, "Vnrr90ghWDrvsL5SK5zs": {"author": {"birth": {"before_common_era": false, "city": "Westland Row, Dublin", "country": "Ireland", "date": {"__time__": "2023-02-10T04:17:36.678Z"}}, "death": {"before_common_era": false, "city": "Paris", "country": "France", "date": {"__time__": "2023-02-10T04:17:36.678Z"}}, "from_reference": {"id": "", "name": ""}, "id": "F3918jwaC9QlGpHC2Rjj", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-10T04:17:36.679Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "Irish poet", "name": "<PERSON>", "summary": "<PERSON><PERSON><PERSON> was an Irish poet and playwright. After writing in different forms throughout the 1880s, the early 1890s saw him become one of the most popular playwrights in London.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOscar Wilde-1610062539926.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON><PERSON>", "youtube": ""}}, "created_at": {"__time__": "2023-02-10T04:19:11.224Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "When the goods wish to punish us, they answer our prayers.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-10T04:16:35.426Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Mr. <PERSON>'s Phone", "release": {"before_common_era": false, "original": {"__time__": "2023-02-10T04:16:35.426Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"spiritual": true}, "updated_at": {"__time__": "2023-02-10T04:19:11.224Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-02-10T04:19:11.224Z"}}, "__collections__": {}}, "VvTy6OBB3KldBMPtdl1N": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-21T02:14:10.534Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-21T02:14:10.534Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-21T02:14:10.534Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-11-21T02:15:15.305Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "When you're going to change the world, don't ask for permission.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-21T02:14:10.535Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2021-11-21T02:14:10.535Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"knowledge": true, "motivation": true}, "updated_at": {"__time__": "2021-11-21T02:15:15.305Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-11-21T02:15:15.305Z"}}, "__collections__": {}}, "Wunn1zvG7aELvlaJj5RK": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-03T15:03:48.488Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-03T15:03:48.488Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-03T13:34:30.250Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-03T15:05:12.413Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le monde, <PERSON><PERSON><PERSON>, c'est comme le temps. On ne le change pas. Et on ne le façonne pas. C'est lui qui nous façonne.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-03T13:34:30.250Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "L'île des Chasseurs d'oiseaux ", "release": {"before_common_era": false, "original": {"__time__": "2023-07-03T13:34:30.250Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "travel": true}, "updated_at": {"__time__": "2023-07-03T15:05:12.413Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-03T15:05:12.413Z"}}, "__collections__": {}}, "Wx7a1CWbepdDxDJwU3cu": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-15T20:40:40.465Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Il a fallu que je perde un testicule pour être un homme meilleur.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-15T20:38:58.275Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Atypical", "release": {"before_common_era": false, "original": {"__time__": "2021-07-15T20:38:58.275Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"biology": true, "fun": true, "mature": true}, "updated_at": {"__time__": "2021-07-15T20:40:40.465Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-15T20:40:40.465Z"}}, "__collections__": {}}, "Wxbxf3XGYl3el9fEaohx": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-02-20T00:20:53.580Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Man's reach exceed his imagination.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Prestige", "release": {"before_common_era": false, "original": {"__time__": "2022-02-19T22:43:23.640Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "work": true}, "updated_at": {"__time__": "2022-02-20T00:20:53.580Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-02-20T00:20:53.580Z"}}, "__collections__": {}}, "WyA5keTtRJyzFztRyHxX": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Man in the cave", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-11-22T23:36:33.862Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "La solitude est souvent la rançon de l'intelligence.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2021-11-22T23:34:15.626Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "motivation": true, "work": true}, "updated_at": {"__time__": "2021-11-22T23:36:33.862Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-11-22T23:36:33.862Z"}}, "__collections__": {}}, "XDBCYWuUEEej3uCGPTz9": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-21T02:14:10.534Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-21T02:14:10.534Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-21T02:14:10.534Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Vi", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-11-21T02:19:49.460Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "You know, <PERSON><PERSON><PERSON>, what makes you different makes you strong.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-21T02:14:10.535Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2021-11-21T02:14:10.535Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"psychology": true}, "updated_at": {"__time__": "2021-11-21T02:19:49.460Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-11-21T02:19:49.460Z"}}, "__collections__": {}}, "XF1XzsaMlhh2wSu3sr1t": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-26T03:21:58.662Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-26T03:21:58.662Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-26T03:21:58.662Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>. <PERSON> the Divine", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-01-26T03:27:47.427Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Les morts ne reviennent pas. Mais certains d'entre-eux ne partent pas.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-26T03:21:58.662Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "MaleVolent", "release": {"before_common_era": false, "original": {"__time__": "2023-01-26T03:21:58.662Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"spiritual": true}, "updated_at": {"__time__": "2023-01-26T03:27:47.427Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-01-26T03:27:47.427Z"}}, "__collections__": {}}, "XWAcECdBhy0oDoPCCyJ1": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-21T02:14:10.534Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-21T02:14:10.534Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-21T02:14:10.534Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-11-21T02:15:50.127Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Pour changer le monde, il ne faut pas demander la permission.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-21T02:14:10.535Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2021-11-21T02:14:10.535Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"knowledge": true, "motivation": true}, "updated_at": {"__time__": "2021-11-21T02:15:50.127Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-11-21T02:15:50.127Z"}}, "__collections__": {}}, "XX10ajjI9ZgeYPzyHghA": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-31T22:01:21.749Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-31T22:01:21.749Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-31T22:01:21.749Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-05-31T22:05:17.421Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Tu devrais toujours chercher des preuves et tu ne devrais jamais t'arrêter de poser des questions.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-31T22:01:21.749Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Pantheon", "release": {"before_common_era": false, "original": {"__time__": "2023-05-31T22:01:21.749Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "psychology": true}, "updated_at": {"__time__": "2023-05-31T22:05:17.421Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-05-31T22:05:17.421Z"}}, "__collections__": {}}, "YBhlnFbMws1IlY5XROeU": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-11T23:22:47.673Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-11T23:22:47.673Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-11T23:22:47.673Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Psy", "summary": "ep 5", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-11T23:25:37.874Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le monde a toujours été merdique et chaotique, cher ami.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-11T23:22:47.673Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Jupiter's Legacy", "release": {"before_common_era": false, "original": {"__time__": "2021-05-11T23:22:47.673Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"philosophy": true}, "updated_at": {"__time__": "2021-05-11T23:25:37.874Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-11T23:25:37.874Z"}}, "__collections__": {}}, "YjjVRQYLFrDDbjgSc1NC": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-07-29T12:34:34.800Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-07-29T12:34:34.800Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-07-29T12:34:34.800Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-07-29T12:42:58.927Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "L'âge est juste un chiffre, vous pouvez <PERSON> ou soustraire, mais à la fin de la journée, quand le temps viendra, vous mourrez de toute façon.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-07-29T12:34:34.801Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "MADiSON", "release": {"before_common_era": false, "original": {"__time__": "2022-07-06T22:00:00.000Z"}}, "summary": "MADiSON is a first person psychological horror game that delivers an immersive and terrifying experience. With the help of an instant camera, connect the human world with the beyond, take pictures and develop them by yourself.", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "philosophy": true, "psychology": true}, "updated_at": {"__time__": "2022-07-29T12:42:58.927Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-07-29T12:42:58.927Z"}}, "__collections__": {}}, "ZOfXwqXZio5ExezmovZ0": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T13:46:29.713Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T13:46:29.713Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T13:46:29.713Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "I.A.L. Diamond", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-31T13:49:03.558Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Je passe plus de temps à réécrire qu'à écrire.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T13:48:18.825Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The screenwriter looks at the screenwriter ", "release": {"before_common_era": false, "original": {"__time__": "1972-07-30T23:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"language": true, "poetry": true, "work": true}, "updated_at": {"__time__": "2023-07-31T13:49:03.558Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-31T13:49:03.558Z"}}, "__collections__": {}}, "Zm15vECR0eUZP5meees3": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T23:41:32.122Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T23:41:32.122Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "<PERSON><PERSON><PERSON>", "name": "Local man", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-29T23:44:50.411Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le mal ne prospère que là où il est le bienvenu.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Things heard and seen", "release": {"before_common_era": false, "original": {"__time__": "2021-05-29T21:59:21.354Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"spiritual": true}, "updated_at": {"__time__": "2021-05-29T23:44:50.411Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-29T23:44:50.411Z"}}, "__collections__": {}}, "ZrY9BIGZE7xGWPHgylg7": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-04-15T13:29:08.850Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-04-15T13:29:08.850Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-04-15T13:29:08.850Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-04-15T13:31:11.215Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Les oiseaux ne chantent pas, ils hurlent de souffrance.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-04-15T13:29:08.851Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "BEEF", "release": {"before_common_era": false, "original": {"__time__": "2023-04-15T13:29:08.851Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"biology": true, "feelings": true}, "updated_at": {"__time__": "2023-04-15T13:31:11.215Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-04-15T13:31:11.215Z"}}, "__collections__": {}}, "aDSw2DPS9g6ntF3gOmnc": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-07-20T20:16:48.176Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-20T20:16:48.177Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-07-20T20:17:55.602Z"}, "language": "en", "name": "\"Nothing is so conducive to spiritual growth as this capacity for logical and accurate analysis of everything that happens to us.\"\n\n— <PERSON>", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-20T20:16:48.177Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-07-20T20:16:48.177Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-07-20T20:18:03.904Z"}, "user": {"id": "2Ya4jgspNkNFOWKbhh4gf5a2bys2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-07-20T20:17:55.603Z"}}, "status": "", "updated_at": {"__time__": "2024-07-20T20:17:55.603Z"}}, "__collections__": {}}, "aEVFO6zzqiBGJjoCe4s6": {"author": {"birth": {"before_common_era": false, "city": "Alençon, Normandy", "country": "France", "date": {"__time__": "2021-10-23T22:46:27.664Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-10-23T22:46:27.664Z"}}, "from_reference": {"id": "", "name": ""}, "id": "tfcJHq7qiyPsN0EZMqUP", "image": {"credits": {"artist": "<PERSON>", "before_common_era": false, "company": "", "date": {"__time__": "2018-12-31T23:00:00.000Z"}, "location": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON> 2019", "url": "https://en.wikipedia.org/wiki/Orelsan?oldformat=true#/media/File:Or<PERSON><PERSON>_Deauville_2019.jpg"}}, "is_fictional": false, "job": "French rapper", "name": "<PERSON><PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON><PERSON>, better known by his stage name <PERSON><PERSON><PERSON>, sometimes stylized as <PERSON><PERSON><PERSON><PERSON>, is a French rapper, songwriter, record producer, actor and film director.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/orelsan", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOrelsan-1619990138849.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/orelsan/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/Orel_San", "website": "https://orelsan.tv/music/", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON><PERSON>an", "youtube": "https://www.youtube.com/channel/UCEnFzIYw3BrndPCddyQ6c5A"}}, "created_at": {"__time__": "2021-10-23T22:46:57.370Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Si tu veux faire un film, t'as juste besoin d'un truc qui filme.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-10-23T22:44:56.384Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Notes pour trop tard", "release": {"before_common_era": false, "original": {"__time__": "2016-12-31T23:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"philosophy": true}, "updated_at": {"__time__": "2021-10-23T22:46:57.370Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-10-23T22:46:57.370Z"}}, "__collections__": {}}, "aMYqhxTOQrATFxErdlEb": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-26T13:25:28.852Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "La plupart des gens croient que l'écriture de scénario n'est que du dialogue (…). La réalité, c'est que la contribution la plus importante d'un scénariste est la structure.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The craft of the screenwriter ", "release": {"before_common_era": false, "original": {"__time__": "1981-07-25T22:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"work": true}, "updated_at": {"__time__": "2023-07-26T13:25:28.852Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-26T13:25:28.852Z"}}, "__collections__": {}}, "bA2BYHxpnzGWPalSQrXf": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-11T23:33:28.898Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-11T23:33:28.898Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-11T23:22:47.673Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "Psychologist", "name": "<PERSON>", "summary": "ep 5", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-11T23:34:04.944Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "La réalité l'emporte toujours, peu importe combien on s'y oppose.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-11T23:22:47.673Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Jupiter's Legacy", "release": {"before_common_era": false, "original": {"__time__": "2021-05-11T23:22:47.673Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "philosophy": true, "psychology": true}, "updated_at": {"__time__": "2021-05-11T23:34:04.944Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-11T23:34:04.944Z"}}, "__collections__": {}}, "bIiHHztSXO2Uoverv117": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-26T15:36:03.909Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-26T15:36:03.909Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-26T15:36:03.909Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-26T15:37:34.817Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Raisonner avec les astrologues, c'est boxer un oreiller de plumes: on l'enfonce en un point, il se regonfle ailleurs.", "reference": {"id": "zknEA6J4kljNQB4LhgP0", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-26T15:37:28.309Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2013-07-17T22:00:00.000Z"}}, "summary": "<PERSON> is the channel of a french YouTuber of the same name, talking about psychology, philosophy, social, among other subjects. He has a videos serie on philosophy where he explains concepts with a modern take.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/cyrusnorth/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyrus North-1610062241274.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/cyrusnorth/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/cyrusnorth", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Cyrus_<PERSON>", "youtube": "https://www.youtube.com/channel/UCah8C0gmLkdtvsy0b2jrjrw"}}, "topics": {"fun": true, "metaphor": true, "poetry": true, "psychology": true}, "updated_at": {"__time__": "2021-05-26T15:37:34.817Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-26T15:37:34.817Z"}}, "__collections__": {}}, "c5lyR00AcOvmF9C1ieYY": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-25T15:44:30.491Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-25T15:44:30.491Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-25T15:44:30.491Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-25T15:46:56.196Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "On prend la vie par tranche de trois secondes.", "reference": {"id": "zknEA6J4kljNQB4LhgP0", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-25T15:45:26.627Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2013-07-17T22:00:00.000Z"}}, "summary": "<PERSON> is the channel of a french YouTuber of the same name, talking about psychology, philosophy, social, among other subjects. He has a videos serie on philosophy where he explains concepts with a modern take.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/cyrusnorth/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyrus North-1610062241274.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/cyrusnorth/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/cyrusnorth", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Cyrus_<PERSON>", "youtube": "https://www.youtube.com/channel/UCah8C0gmLkdtvsy0b2jrjrw"}}, "topics": {"philosophy": true, "poetry": true}, "updated_at": {"__time__": "2021-05-25T15:46:56.196Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-25T15:46:56.196Z"}}, "__collections__": {}}, "cEk11M1Scn6hLhrLnLT5": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-27T23:10:19.921Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-27T23:10:19.921Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-27T23:10:19.921Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-27T23:14:32.813Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Tant que vous n'avez pas rendu l'inconscient conscient, il dirigera votre vie. Et vous appellerez cela le destin.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-27T23:10:19.921Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Sinner", "release": {"before_common_era": false, "original": {"__time__": "2021-07-27T23:10:19.921Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "philosophy": true, "retrospection": true}, "updated_at": {"__time__": "2021-07-27T23:14:32.813Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-27T23:14:32.813Z"}}, "__collections__": {}}, "cgJgoV092RuVI9KOwzoJ": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-27T14:30:47.081Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-27T14:30:47.081Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-27T14:30:47.081Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-27T14:54:02.929Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le devoir de la comédie est de corriger les hommes en les divertissant.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-27T14:30:47.081Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Premier placet du Tartuffe", "release": {"before_common_era": false, "original": {"__time__": "1664-07-26T22:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "fun": true, "motivation": true, "psychology": true, "social": true}, "updated_at": {"__time__": "2023-07-27T14:54:02.929Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-27T14:54:02.929Z"}}, "__collections__": {}}, "dQBdUuUHTMDO7QqVXa2o": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-08-04T19:32:46.889Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-08-04T19:32:46.889Z"}}, "from_reference": {"id": "z2gmE4AfOjaZbnBirVvE", "name": ""}, "id": "YKQGg4TbHX4m7XI8smhd", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-08-04T19:32:46.889Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": true, "job": "<PERSON>", "name": "<PERSON><PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON> is a fictional character played by <PERSON><PERSON> in the TV series \"<PERSON><PERSON><PERSON> (The Gift)\". In the show, she's a painter who has strange visions and is fascinated with a particular symbol. She will start a travel in search of the truth.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FAtiye-1604423792750.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>", "youtube": ""}}, "created_at": {"__time__": "2021-08-04T19:33:07.107Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Time is an eternal moment.", "reference": {"id": "z2gmE4AfOjaZbnBirVvE", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-08-04T19:32:59.079Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Gift (Atiye)", "release": {"before_common_era": false, "original": {"__time__": "2019-12-26T23:00:00.000Z"}}, "summary": "The Gift (known in Turkey as At<PERSON><PERSON>) is a Turkish psychological thriller Netflix series starring <PERSON><PERSON>. It was written by <PERSON> and <PERSON><PERSON><PERSON>. The first season consists of 8 episodes and became available for streaming on Netflix on December 27, 2019.[1][2] The series is an adaptation of the novel <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON>. The show tells the story of a painter in Istanbul embarks on a personal journey as she unearths universal secrets about an Anatolian archaeological site and its link to her past.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Gift (Atiye)-1604423035059.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/81037848", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Gift_(Turkish_TV_series)", "youtube": ""}}, "topics": {"philosophy": true, "poetry": true}, "updated_at": {"__time__": "2021-08-04T19:33:07.107Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-08-04T19:33:07.107Z"}}, "__collections__": {}}, "dyG33sSZYEfKrj1r0Lxl": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": null}, "death": {"before_common_era": false, "city": "", "country": "", "date": null}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "CEO", "name": "Teest", "summary": "qweqe", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2020-09-16T20:50:14.590Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "lets try this and see", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "", "name": "", "release": {"before_common_era": false, "original": null}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "introspection": true, "knowledge": true}, "updated_at": {"__time__": "2020-09-16T20:50:14.590Z"}, "user": {"id": "ngzHLKzLx8YgGEfuIzkORvcM3vr2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "proposed", "updated_at": {"__time__": "2020-09-16T20:50:14.590Z"}}, "__collections__": {}}, "e0zalEH15pkZEmx6Q1Yx": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-11-22T23:43:17.279Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "C'est triste, mais plus on brille, plus on s'éteint vite.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2021-11-22T23:34:15.626Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"knowledge": true, "poetry": true, "retrospection": true, "work": true}, "updated_at": {"__time__": "2021-11-22T23:43:17.279Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-11-22T23:43:17.279Z"}}, "__collections__": {}}, "eG4ottsJL9qyD6NkbIbz": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-29T08:11:38.013Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-29T08:11:38.013Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-29T08:08:33.041Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-29T08:12:02.529Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "\"Données\" n'est pas le pluriel de \"anecdote\".", "reference": {"id": "qMG13EMdHApbx9zXvrAd", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-29T08:09:13.031Z"}, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "Dirty Biology", "release": {"before_common_era": false, "original": {"__time__": "2021-07-29T08:09:13.031Z"}}, "summary": "Sciences videos on mindfuck subject, dirty or just fun. Sometimes we talk about biology too.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FDirty%20Biology.jpg?alt=media&token=9ab3b79e-ecca-4a20-8793-2b1eb8435b76", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/user/dirtybiology", "wikipedia": "", "youtube": ""}}, "topics": {"sciences": true}, "updated_at": {"__time__": "2021-07-29T08:12:02.529Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-29T08:12:02.529Z"}}, "__collections__": {}}, "eL2w3VniCWTx9SKfCprN": {"author": {"birth": {"before_common_era": false, "city": "Boston, Massachusetts", "country": "United States", "date": {"__time__": "2023-01-14T23:50:37.444Z"}}, "death": {"before_common_era": false, "city": "Church Home & Hospital, Baltimore, Maryland", "country": "United States", "date": {"__time__": "2023-01-14T23:50:37.444Z"}}, "from_reference": {"id": "", "name": ""}, "id": "Mgy3JSjy83idrrzp26lP", "image": {"credits": {"artist": "Restored by <PERSON><PERSON> and <PERSON>", "before_common_era": false, "company": "", "date": {"__time__": "1849-05-31T22:00:00.000Z"}, "location": "", "name": "1849 \"Annie\" daguerreotype of Poe", "url": "https://en.wikipedia.org/wiki/<PERSON>?oldformat=true#/media/File:<PERSON>_<PERSON>,_circa_1849,_restored,_squared_off.jpg"}}, "is_fictional": false, "job": "American writer", "name": "<PERSON>", "summary": "<PERSON> was an American writer, poet, editor, and literary critic. <PERSON> is best known for his poetry and short stories, particularly his tales of mystery and the macabre.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FEd<PERSON>-1619982678027.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>", "youtube": ""}}, "created_at": {"__time__": "2023-01-14T23:51:07.975Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Believe nothing you hear and half of what you see.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-14T23:49:35.762Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Wednesday", "release": {"before_common_era": false, "original": {"__time__": "2023-01-14T23:49:35.762Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "poetry": true}, "updated_at": {"__time__": "2023-01-14T23:51:07.975Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-01-14T23:51:07.975Z"}}, "__collections__": {}}, "ecdPSGZw3R0QY3ZGVOES": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T13:43:31.690Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T13:43:31.690Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T13:43:31.690Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-31T13:45:57.169Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Une merveille absurde est pour moi dans appas.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T13:45:23.335Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "L'art poétique ", "release": {"before_common_era": false, "original": {"__time__": "1674-07-30T22:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true}, "updated_at": {"__time__": "2023-07-31T13:45:57.169Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-31T13:45:57.169Z"}}, "__collections__": {}}, "f2jIx3v9W4LeX9qlitSx": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-18T21:20:07.837Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-18T21:20:07.837Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-18T21:20:07.837Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-03-18T21:31:10.850Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Oublier ce que tu es pour devenir ce que les autres ont besoin. C'est peut-être ça le vrai sens de la vie.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-18T21:20:07.837Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Detroit: Become Human", "release": {"before_common_era": false, "original": {"__time__": "2023-03-18T21:20:07.837Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "philosophy": true, "social": true}, "updated_at": {"__time__": "2023-03-18T21:31:10.850Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-03-18T21:31:10.850Z"}}, "__collections__": {}}, "fXmWW307ud4JNHveL5pb": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-04-15T13:29:08.850Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-04-15T13:29:08.850Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-04-15T13:29:08.850Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-04-15T13:30:12.536Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "The birds don't sing, they screech in pain.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-04-15T13:29:08.851Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "BEEF", "release": {"before_common_era": false, "original": {"__time__": "2023-04-15T13:29:08.851Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"biology": true, "feelings": true}, "updated_at": {"__time__": "2023-04-15T13:30:12.536Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-04-15T13:30:12.536Z"}}, "__collections__": {}}, "fmk70eFey3SDiKg5suhm": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-29T00:50:29.390Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-29T00:50:29.390Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-29T00:50:29.390Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Erin", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-09-29T00:52:50.329Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Nous sommes le cosmos qui rêve de lui-même.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-29T00:50:29.390Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Midnight Mass", "release": {"before_common_era": false, "original": {"__time__": "2021-09-29T00:50:29.390Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"poetry": true, "sciences": true}, "updated_at": {"__time__": "2021-09-29T00:52:50.329Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-09-29T00:52:50.329Z"}}, "__collections__": {}}, "foeiZke6COhP7d2BrjON": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-18T21:20:07.837Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-18T21:20:07.837Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-18T21:20:07.837Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-03-18T21:32:10.274Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "To forget what you are to become what other need you to be. Maybe that's the sense of life.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-18T21:20:07.837Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Detroit: Become Human", "release": {"before_common_era": false, "original": {"__time__": "2023-03-18T21:20:07.837Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "philosophy": true, "social": true}, "updated_at": {"__time__": "2023-03-18T21:32:10.274Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-03-18T21:32:10.274Z"}}, "__collections__": {}}, "ft7inccZYnIBmfvBIsbX": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-29T16:49:15.045Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-29T16:49:15.045Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-29T16:49:15.045Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-29T16:55:38.631Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Une théorie sans faits n'est qu'une fantasie, mais des faits sans théorie ne sont que chaos.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-29T16:49:15.045Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Les sciences face aux créationnismes", "release": {"before_common_era": false, "original": {"__time__": "2023-07-29T16:49:15.045Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"knowledge": true, "sciences": true}, "updated_at": {"__time__": "2023-07-29T16:55:38.631Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-29T16:55:38.631Z"}}, "__collections__": {}}, "fwwsiP5KXMoedsgJAgZq": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-27T12:46:37.247Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-27T12:46:37.247Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-27T12:46:37.247Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-11-27T12:47:49.179Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "L'existence précédée l'essence.", "reference": {"id": "zknEA6J4kljNQB4LhgP0", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-27T12:47:41.308Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2013-07-17T22:00:00.000Z"}}, "summary": "<PERSON> is the channel of a french YouTuber of the same name, talking about psychology, philosophy, social, among other subjects. He has a videos serie on philosophy where he explains concepts with a modern take.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/cyrusnorth/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyrus North-1610062241274.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/cyrusnorth/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/cyrusnorth", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Cyrus_<PERSON>", "youtube": "https://www.youtube.com/channel/UCah8C0gmLkdtvsy0b2jrjrw"}}, "topics": {"philosophy": true}, "updated_at": {"__time__": "2021-11-27T12:47:49.179Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-11-27T12:47:49.179Z"}}, "__collections__": {}}, "h3yCpMTOdeUUYCV5Qpxq": {"author": {"birth": {"before_common_era": false, "city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Germany", "date": {"__time__": "2023-06-04T12:29:06.850Z"}}, "death": {"before_common_era": false, "city": "Weimar", "country": "Germany", "date": {"__time__": "2023-06-04T12:29:06.850Z"}}, "from_reference": {"id": "", "name": ""}, "id": "zROlefQfE7E9w04wcFUV", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-06-04T12:29:06.850Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "Philosopher", "name": "<PERSON>", "summary": "<PERSON> was a German philosopher who became one of the most influential of all modern thinkers. His attempts to unmask the motives that underlie traditional Western religion, morality, and philosophy deeply affected generations of theologians, philosophers, psychologists, poets, novelists, and playwrights.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FF<PERSON><PERSON> Nietzsche-1614727502837.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON>", "youtube": ""}}, "created_at": {"__time__": "2023-06-04T12:29:22.530Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Ma solitude ne dépend pas de la présence ou de l'absence de personnes ; au contraire, je déteste celui qui vole ma solitude sans, en échange, m'offrir une vraie compagnie.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-06-04T12:27:06.629Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2023-06-04T12:27:06.629Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "social": true}, "updated_at": {"__time__": "2023-06-04T12:29:22.530Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-06-04T12:29:22.530Z"}}, "__collections__": {}}, "hTExxQucZZv83K0aCu68": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2025-03-05T05:28:05.223Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2025-03-05T05:28:05.223Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON><PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "categories": {}, "created_at": {"__time__": "2025-03-05T05:29:54.951Z"}, "language": "en", "name": "Professional goals are temporary but personal loss is permanent. ", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2025-03-05T05:28:05.223Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2025-03-05T05:28:05.223Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "tags": {}, "topics": {}, "updated_at": {"__time__": "2025-03-05T05:30:33.299Z"}, "user": {"id": "iWucE3t4czLIaZkm06LfIrsfVP03"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2025-03-05T05:29:54.952Z"}}, "status": "", "updated_at": {"__time__": "2025-03-05T05:29:54.952Z"}}, "__collections__": {}}, "hVelqIR0Qy7SUcvlOI91": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-06-13T09:27:42.962Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-06-13T09:27:42.962Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-13T09:27:42.962Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Emilia", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-06-13T09:29:04.996Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "It's more satisfying to hear a single \"thank you\" than a lot of \"sorry\"s.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-13T09:27:42.963Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Re:zero", "release": {"before_common_era": false, "original": {"__time__": "2021-06-13T09:27:42.963Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true}, "updated_at": {"__time__": "2021-06-13T09:29:04.996Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-06-13T09:29:04.996Z"}}, "__collections__": {}}, "iKQOcuyCKvnN1jM8tIUG": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-13T23:40:11.333Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-13T23:40:11.333Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-13T23:40:11.333Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-03-13T23:41:56.660Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Life is but a shipwreck of our plans.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-13T23:40:11.333Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Shape of Water", "release": {"before_common_era": false, "original": {"__time__": "2023-03-13T23:40:11.333Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"metaphor": true, "poetry": true, "travel": true}, "updated_at": {"__time__": "2023-03-13T23:41:56.660Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-03-13T23:41:56.660Z"}}, "__collections__": {}}, "ikDReRM5jIRYw60nOM83": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-06-07T19:27:41.128Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-07T19:27:41.128Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-06-07T19:28:14.775Z"}, "language": "en", "name": "Houston, we have a problem.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-07T19:27:41.128Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Apollo 13", "release": {"before_common_era": false, "original": {"__time__": "2024-06-07T19:27:41.128Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-06-07T19:28:15.961Z"}, "user": {"id": "p4PWrK73GUWam8wxaOmE64zKFR52"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-06-07T19:28:14.776Z"}}, "status": "", "updated_at": {"__time__": "2024-06-07T19:28:14.776Z"}}, "__collections__": {}}, "ivcyE3qWETIOrrKdnhe7": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T23:27:13.259Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T23:27:13.259Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "<PERSON><PERSON><PERSON>", "name": "<PERSON>", "summary": "He's Mare husband.", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-29T23:27:27.885Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "On ne veut pas du sh<PERSON>rif quand on est ivre.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Things heard and seen", "release": {"before_common_era": false, "original": {"__time__": "2021-05-29T21:59:21.354Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"fun": true}, "updated_at": {"__time__": "2021-05-29T23:27:27.885Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-29T23:27:27.885Z"}}, "__collections__": {}}, "j5WdlAmE3kw2AFl8evCl": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-20T10:22:47.278Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-20T10:22:47.278Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-20T10:22:47.278Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-05-20T10:24:13.808Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "You may live to see man-made horrors beyond your comprehension.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-20T10:22:47.278Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Fireship", "release": {"before_common_era": false, "original": {"__time__": "2023-05-20T10:22:47.278Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"psychology": true, "social": true, "travel": true}, "updated_at": {"__time__": "2023-05-20T10:24:13.808Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-05-20T10:24:13.808Z"}}, "__collections__": {}}, "jK3ZlGNOlDTeT4OjVrVD": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-02-19T23:06:27.523Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "La société ne tolère qu'un changement à la fois.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Prestige", "release": {"before_common_era": false, "original": {"__time__": "2022-02-19T22:43:23.640Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"sciences": true, "social": true}, "updated_at": {"__time__": "2022-02-19T23:06:27.523Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-02-19T23:06:27.523Z"}}, "__collections__": {}}, "km6xWy3jDXcmey1gHpcp": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-28T10:39:20.845Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-28T10:39:20.845Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-28T10:39:20.845Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-28T10:40:37.601Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "On parle probablement plus de sexe que d'autres choses.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-28T10:39:20.845Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "arte", "release": {"before_common_era": false, "original": {"__time__": "2023-07-28T10:39:20.845Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "language": true, "philosophy": true, "social": true}, "updated_at": {"__time__": "2023-07-28T10:40:37.601Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-28T10:40:37.601Z"}}, "__collections__": {}}, "kyweAJe1WkjPnJipRhGr": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-08-24T10:55:02.759Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-24T10:55:02.759Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "Socrates", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "categories": {}, "created_at": {"__time__": "2024-08-24T10:55:22.579Z"}, "language": "en", "name": "The secret of happiness, you see, is not found in seeking more, but in developing the capacity to enjoy less. ", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-24T10:55:02.759Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-08-24T10:55:02.759Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "tags": {}, "topics": {}, "updated_at": {"__time__": "2024-08-24T10:55:29.373Z"}, "user": {"id": "7Binj8yNatVki43rPX7RfYAhMKA2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-08-24T10:55:22.580Z"}}, "status": "", "updated_at": {"__time__": "2024-08-24T10:55:22.580Z"}}, "__collections__": {}}, "lK1qsNSMKRpB9jRReyqF": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-25T15:44:30.491Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-25T15:44:30.491Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-25T15:44:30.491Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-25T15:46:30.702Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "We take life three seconds at a time.", "reference": {"id": "zknEA6J4kljNQB4LhgP0", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-25T15:45:26.627Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2013-07-17T22:00:00.000Z"}}, "summary": "<PERSON> is the channel of a french YouTuber of the same name, talking about psychology, philosophy, social, among other subjects. He has a videos serie on philosophy where he explains concepts with a modern take.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/cyrusnorth/", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FCyrus North-1610062241274.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/cyrusnorth/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/cyrusnorth", "website": "", "wikipedia": "https://fr.wikipedia.org/wiki/Cyrus_<PERSON>", "youtube": "https://www.youtube.com/channel/UCah8C0gmLkdtvsy0b2jrjrw"}}, "topics": {"philosophy": true, "poetry": true}, "updated_at": {"__time__": "2021-05-25T15:46:30.702Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-25T15:46:30.702Z"}}, "__collections__": {}}, "ln7XXFf9eS8WL3kF1ITz": {"author": {"birth": {"before_common_era": false, "city": "Alençon, Normandy", "country": "France", "date": {"__time__": "2021-10-24T09:47:37.690Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-10-24T09:47:37.690Z"}}, "from_reference": {"id": "", "name": ""}, "id": "tfcJHq7qiyPsN0EZMqUP", "image": {"credits": {"artist": "<PERSON>", "before_common_era": false, "company": "", "date": {"__time__": "2018-12-31T23:00:00.000Z"}, "location": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON> 2019", "url": "https://en.wikipedia.org/wiki/Orelsan?oldformat=true#/media/File:Or<PERSON><PERSON>_Deauville_2019.jpg"}}, "is_fictional": false, "job": "French rapper", "name": "<PERSON><PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON><PERSON>, better known by his stage name <PERSON><PERSON><PERSON>, sometimes stylized as <PERSON><PERSON><PERSON><PERSON>, is a French rapper, songwriter, record producer, actor and film director.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/orelsan", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOrelsan-1619990138849.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/orelsan/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/Orel_San", "website": "https://orelsan.tv/music/", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON><PERSON>an", "youtube": "https://www.youtube.com/channel/UCEnFzIYw3BrndPCddyQ6c5A"}}, "created_at": {"__time__": "2021-10-24T09:48:15.353Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Ce que je raconte dans mes chansons c'est des clichés, c'est pas la vérité.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-10-24T09:46:31.562Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Peur de l'échec", "release": {"before_common_era": false, "original": {"__time__": "2008-12-31T23:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true}, "updated_at": {"__time__": "2021-10-24T09:48:15.353Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-10-24T09:48:15.353Z"}}, "__collections__": {}}, "m2oFJ4JNylzicpjIy5Fr": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-25T16:16:48.558Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-25T16:16:48.558Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-25T16:16:48.558Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": true, "job": "", "name": "<PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-03-25T16:19:34.336Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "L'histoire vie en nous, que nous l'apprenions ou pas.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-25T16:16:48.558Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2023-03-25T16:16:48.558Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "knowledge": true, "travel": true}, "updated_at": {"__time__": "2023-03-25T16:19:34.336Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-03-25T16:19:34.336Z"}}, "__collections__": {}}, "m3LA2d7iVmg0Ydv0bi9v": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-08-21T19:03:43.625Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-21T19:03:43.625Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "categories": {}, "created_at": {"__time__": "2024-08-21T19:04:21.449Z"}, "language": "en", "name": "Run Forrest!", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-21T19:03:43.625Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2024-08-21T19:03:43.625Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "tags": {}, "topics": {}, "updated_at": {"__time__": "2024-08-21T19:04:30.006Z"}, "user": {"id": "uhhGbR41rMY5Y8GlyvxxI3nUgCr2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-08-21T19:04:21.450Z"}}, "status": "", "updated_at": {"__time__": "2024-08-21T19:04:21.450Z"}}, "__collections__": {}}, "mBNkynY4U43DUkCPKvcQ": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T13:46:29.713Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T13:46:29.713Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T13:46:29.713Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-31T13:47:24.731Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "J'ai eu l'immense bonheur d'amuser les autres en m'amusant.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T13:46:49.765Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Les Cahiers de la Bande Dessinée n°22", "release": {"before_common_era": false, "original": {"__time__": "1973-07-30T23:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true}, "updated_at": {"__time__": "2023-07-31T13:47:24.731Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-31T13:47:24.731Z"}}, "__collections__": {}}, "mERmPNFW1lvOlGVAL2uy": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-09-05T08:10:56.961999Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-09-05T08:10:56.961999Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "Plutarch", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "categories": {}, "created_at": {"__time__": "2024-09-05T08:11:17.204Z"}, "language": "en", "name": "\"The mind is not a vessel to be filled but a fire to be kindled.\" ", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-09-05T08:10:56.961999Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-09-05T08:10:56.961999Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "tags": {}, "topics": {}, "updated_at": {"__time__": "2024-09-05T08:11:24.155Z"}, "user": {"id": "7Binj8yNatVki43rPX7RfYAhMKA2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-09-05T08:11:17.204Z"}}, "status": "", "updated_at": {"__time__": "2024-09-05T08:11:17.204Z"}}, "__collections__": {}}, "mbhupEcx9NWodbj0jBCd": {"author": {"birth": {"before_common_era": false, "city": "Alençon, Normandy", "country": "France", "date": {"__time__": "2021-10-24T14:11:45.413Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-10-24T14:11:45.413Z"}}, "from_reference": {"id": "", "name": ""}, "id": "tfcJHq7qiyPsN0EZMqUP", "image": {"credits": {"artist": "<PERSON>", "before_common_era": false, "company": "", "date": {"__time__": "2018-12-31T23:00:00.000Z"}, "location": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON> 2019", "url": "https://en.wikipedia.org/wiki/Orelsan?oldformat=true#/media/File:Or<PERSON><PERSON>_Deauville_2019.jpg"}}, "is_fictional": false, "job": "French rapper", "name": "<PERSON><PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON><PERSON>, better known by his stage name <PERSON><PERSON><PERSON>, sometimes stylized as <PERSON><PERSON><PERSON><PERSON>, is a French rapper, songwriter, record producer, actor and film director.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/orelsan", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOrelsan-1619990138849.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/orelsan/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/Orel_San", "website": "https://orelsan.tv/music/", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON><PERSON>an", "youtube": "https://www.youtube.com/channel/UCEnFzIYw3BrndPCddyQ6c5A"}}, "created_at": {"__time__": "2021-10-24T14:12:25.439Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "C'est nous le futur.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-10-24T14:11:05.403Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2010-12-31T23:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true}, "updated_at": {"__time__": "2021-10-24T14:12:25.439Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-10-24T14:12:25.439Z"}}, "__collections__": {}}, "meRZYYPVYvJzrtVcbiUY": {"author": {"birth": {"before_common_era": false, "city": "Alençon, Normandy", "country": "France", "date": {"__time__": "2021-10-24T20:40:37.138Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-10-24T20:40:37.138Z"}}, "from_reference": {"id": "", "name": ""}, "id": "tfcJHq7qiyPsN0EZMqUP", "image": {"credits": {"artist": "<PERSON>", "before_common_era": false, "company": "", "date": {"__time__": "2018-12-31T23:00:00.000Z"}, "location": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON> 2019", "url": "https://en.wikipedia.org/wiki/Orelsan?oldformat=true#/media/File:Or<PERSON><PERSON>_Deauville_2019.jpg"}}, "is_fictional": false, "job": "French rapper", "name": "<PERSON><PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON><PERSON>, better known by his stage name <PERSON><PERSON><PERSON>, sometimes stylized as <PERSON><PERSON><PERSON><PERSON>, is a French rapper, songwriter, record producer, actor and film director.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/orelsan", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOrelsan-1619990138849.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/orelsan/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/Orel_San", "website": "https://orelsan.tv/music/", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON><PERSON>an", "youtube": "https://www.youtube.com/channel/UCEnFzIYw3BrndPCddyQ6c5A"}}, "created_at": {"__time__": "2021-10-24T20:41:24.124Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "J'continuerai mes conneries jusqu'à ce qu'ils me prennent au sérieux.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-10-24T20:39:04.585Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "À l'heure où je me couche", "release": {"before_common_era": false, "original": {"__time__": "2021-10-24T20:39:04.585Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"motivation": true}, "updated_at": {"__time__": "2021-10-24T20:41:24.124Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-10-24T20:41:24.124Z"}}, "__collections__": {}}, "naO8OfmBnLawov8liL6h": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-02-19T22:45:15.815Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Society only tolerates one change at a time.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Prestige", "release": {"before_common_era": false, "original": {"__time__": "2022-02-19T22:43:23.640Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"sciences": true, "social": true}, "updated_at": {"__time__": "2022-02-19T22:45:15.815Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-02-19T22:45:15.815Z"}}, "__collections__": {}}, "ngrSWkScB1CbwHOsjRyw": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-03T00:21:09.182Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-03T00:21:09.182Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-03T00:21:09.182Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON>'s mother", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-03T00:24:19.555Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Peut importe comment ça commence. L'important, c'est comment cela fini.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-03T00:21:09.182Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Re:zero", "release": {"before_common_era": false, "original": {"__time__": "2021-07-03T00:21:09.182Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"psychology": true}, "updated_at": {"__time__": "2021-07-03T00:24:19.555Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-03T00:24:19.555Z"}}, "__collections__": {}}, "ong89GyFF6JS968F6aVh": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-11T01:49:52.834Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-11T01:49:52.834Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-11T01:49:52.834Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-03-11T01:53:06.798Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "The only real philosophical question is suicide. You either accept the absurdity of life, or opt out.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-11T01:49:52.834Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Sinner", "release": {"before_common_era": false, "original": {"__time__": "2023-03-11T01:49:52.834Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "mature": true, "philosophy": true, "psychology": true}, "updated_at": {"__time__": "2023-03-11T01:53:06.798Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-03-11T01:53:06.798Z"}}, "__collections__": {}}, "oxZfqC3VHN2VvHHPJxUu": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-31T03:10:31.468Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-31T03:10:31.468Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-31T03:10:31.468Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>hams", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-31T03:13:30.882Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "<PERSON><PERSON> says that patience is imagining the rose just by looking at the thorn.", "reference": {"id": "z2gmE4AfOjaZbnBirVvE", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-31T03:12:01.656Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Gift (Atiye)", "release": {"before_common_era": false, "original": {"__time__": "2019-12-26T23:00:00.000Z"}}, "summary": "The Gift (known in Turkey as At<PERSON><PERSON>) is a Turkish psychological thriller Netflix series starring <PERSON><PERSON>. It was written by <PERSON> and <PERSON><PERSON><PERSON>. The first season consists of 8 episodes and became available for streaming on Netflix on December 27, 2019.[1][2] The series is an adaptation of the novel <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON>. The show tells the story of a painter in Istanbul embarks on a personal journey as she unearths universal secrets about an Anatolian archaeological site and its link to her past.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FThe Gift (Atiye)-1604423035059.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "https://www.netflix.com/title/81037848", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/The_Gift_(Turkish_TV_series)", "youtube": ""}}, "topics": {"biology": true, "introspection": true, "metaphor": true}, "updated_at": {"__time__": "2021-07-31T03:13:30.882Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-31T03:13:30.882Z"}}, "__collections__": {}}, "p3Ds8iehdZuLBpgHH76j": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T12:34:33.435Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T12:34:33.435Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T12:34:33.435Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-31T12:37:39.715Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Truth can sometimes not be plausible.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T12:34:33.435Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "L'art poétique ", "release": {"before_common_era": false, "original": {"__time__": "1674-07-30T22:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "psychology": true}, "updated_at": {"__time__": "2023-07-31T12:37:39.715Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-31T12:37:39.715Z"}}, "__collections__": {}}, "pgbiykQZSCMAemRWAzSf": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-11-12T14:53:23.438Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-11-12T14:53:23.438Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-11-12T14:53:23.438Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-11-12T14:57:31.687Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "They say it's better not to dream in Fishtown. It hurts too much to wake up.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-11-12T14:53:23.438Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Dispatch from Elsewhere", "release": {"before_common_era": false, "original": {"__time__": "2022-11-12T14:53:23.438Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "introspection": true, "poetry": true}, "updated_at": {"__time__": "2022-11-12T14:57:31.687Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-11-12T14:57:31.687Z"}}, "__collections__": {}}, "pkCgAzW9ztIjZ4Ecel17": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-03T23:46:56.387Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-03T23:46:56.387Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-03T23:46:56.387Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-09-03T23:48:05.823Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "<PERSON>ez-moi mais la boucle fermée par pitié.", "reference": {"id": "M5HNY6m2ZaJ5PSHGtLyy", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-03T23:47:47.545Z"}, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "La Cité de la peur", "release": {"before_common_era": false, "original": {"__time__": "2021-09-03T23:47:47.545Z"}}, "summary": "La Cité de la peur (French: \"The City of Fear\"), also known as Le film de Les Nuls (\"The Les Nuls Movie\"), is a 1994 French comedy film written by and starring <PERSON><PERSON>, <PERSON> and <PERSON>. The movie parodies big budget American films (Basic Instinct, Pretty Woman and The Terminator, among others, are directly spoofed) and relies heavily on puns and word play, which makes it somewhat inaccessible for non-French speakers. ", "urls": {"amazon": "", "facebook": "", "image": "https://upload.wikimedia.org/wikipedia/en/a/ac/La_Cit%C3%A9_de_la_peur.jpg", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "https://en.wikipedia.org/wiki/La_Cit%C3%A9_de_la_peur", "youtube": ""}}, "topics": {"fun": true}, "updated_at": {"__time__": "2021-09-03T23:48:05.823Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-09-03T23:48:05.823Z"}}, "__collections__": {}}, "pnnfvc5DMioR44ECkKzL": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-15T19:42:00.379Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-15T19:42:00.379Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-15T19:42:00.379Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-15T19:42:57.955Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Son of a baked potato.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-15T19:42:00.380Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Atypical", "release": {"before_common_era": false, "original": {"__time__": "2021-07-15T19:42:00.380Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"fun": true}, "updated_at": {"__time__": "2021-07-15T19:42:57.955Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-15T19:42:57.955Z"}}, "__collections__": {}}, "px1yiBBQKMDBq7iwmuGZ": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Man in the cave", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-11-22T23:36:06.406Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Loneliness is often the byproduct of a gifted mind.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-11-22T23:34:15.626Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2021-11-22T23:34:15.626Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "motivation": true, "work": true}, "updated_at": {"__time__": "2021-11-22T23:36:06.406Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-11-22T23:36:06.406Z"}}, "__collections__": {}}, "pymaF5fFQeE9fjyxCuV5": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-10-11T00:55:00.096Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-10-11T00:58:58.214Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Mieux vaut mourir dignement quand il n'est plus possible de vivre dignement.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Midnight Club", "release": {"before_common_era": false, "original": {"__time__": "2022-10-11T00:55:00.097Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "philosophy": true, "poetry": true}, "updated_at": {"__time__": "2022-10-11T00:58:58.214Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-10-11T00:58:58.214Z"}}, "__collections__": {}}, "q0wF25SJ4l76Uowv7yfF": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": null}, "death": {"before_common_era": false, "city": "", "country": "", "date": null}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-11T08:29:53.142Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Firefox design team", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-06-11T08:31:14.719Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "What inspires us the most are the people that love and use Firefox.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-11T08:29:53.142Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Mozilla Blog", "release": {"before_common_era": false, "original": null}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"social": true, "work": true}, "updated_at": {"__time__": "2021-06-11T08:31:14.719Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-06-11T08:31:14.719Z"}}, "__collections__": {}}, "qZBubsKKUuJNpMpmG4lH": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-06-02T23:22:30.702Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-06-02T23:22:30.702Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-06-02T23:22:30.702Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-06-02T23:27:08.447Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Redéfinis les objectifs d'autrui pour qu'ils s'alignent avec les tiens.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-06-02T23:22:30.702Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Pantheon ", "release": {"before_common_era": false, "original": {"__time__": "2023-06-02T23:22:30.702Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "language": true, "psychology": true, "social": true}, "updated_at": {"__time__": "2023-06-02T23:27:08.447Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-06-02T23:27:08.447Z"}}, "__collections__": {}}, "qlFfwTQ5epsbVSJQFnwF": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-12-08T11:55:02.745Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-12-08T11:55:02.745Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-12-08T11:55:02.745Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-12-08T11:58:41.195Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "La plus ancienne et forte émotion de l'humanité est la peur, et la plus ancienne et forte des peurs est la peur de l'inconnu.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-12-08T11:55:02.745Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Endless", "release": {"before_common_era": false, "original": {"__time__": "2022-12-08T11:55:02.745Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "retrospection": true, "social": true}, "updated_at": {"__time__": "2022-12-08T11:58:41.195Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-12-08T11:58:41.195Z"}}, "__collections__": {}}, "qlJitDGvpWdBmi4Ns0un": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T13:20:55.634Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T13:20:55.634Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON>", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-31T13:22:46.281Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "<PERSON> d'âne m'était conté, j'y prendrais un plaisir extrême.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T13:20:55.634999Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Le pouvoir des fables", "release": {"before_common_era": false, "original": {"__time__": "1668-07-30T22:00:00.000Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "poetry": true}, "updated_at": {"__time__": "2024-01-19T17:25:52.361Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-01-19T17:25:48.196Z"}}, "status": "", "updated_at": {"__time__": "2023-07-31T13:22:46.281Z"}}, "__collections__": {}}, "r30WGHvEHjXGsQeOgJUe": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-07-21T11:52:50.611Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-21T11:52:50.611Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "Stuntman <PERSON> ", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-07-21T12:00:25.310Z"}, "language": "en", "name": "The woods are lovely, dark, and deep. And I have promises to keep. And miles to go before I sleep. Did you hear me, <PERSON>? Miles to go, before you sleep.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-07-21T11:52:50.611Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Death proof, <PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2024-07-21T11:52:50.611Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-07-21T12:00:31.425Z"}, "user": {"id": "SxaIImsRNLbUntbvUkNvBvdKR6P2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-07-21T12:00:25.310Z"}}, "status": "", "updated_at": {"__time__": "2024-07-21T12:00:25.310Z"}}, "__collections__": {}}, "rHUccTyXsFCcHJMAvhNw": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T13:30:16.050Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T13:30:16.050Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-26T13:31:27.671Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le génie qui dispose les incidents paraît plus rare que ce celui qui trouve 'es vrais discours.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T13:17:27.108Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "De la poésie dramatique ", "release": {"before_common_era": false, "original": {"__time__": "1981-07-25T22:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"work": true}, "updated_at": {"__time__": "2023-07-26T13:31:27.671Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-26T13:31:27.671Z"}}, "__collections__": {}}, "rHybr8ioOMRRliioNkZe": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-14T23:56:42.612Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-14T23:56:42.612Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-14T23:56:42.612Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Sartre", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-01-14T23:58:03.196Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "L'enfer, c'est les autres.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-14T23:56:42.612Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Wednesday", "release": {"before_common_era": false, "original": {"__time__": "2023-01-14T23:56:42.612Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "introspection": true, "social": true}, "updated_at": {"__time__": "2023-01-14T23:58:03.196Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-01-14T23:58:03.196Z"}}, "__collections__": {}}, "rOzosWoDGa3tCwuGEJVg": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-02-02T05:39:54.753Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-02-02T05:39:54.753Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-02T05:39:54.753Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-02-02T05:45:58.978Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Quand la mort approche, tu fais plus attention aux choses.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-02T05:39:54.753Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Downsizing", "release": {"before_common_era": false, "original": {"__time__": "2023-02-02T05:39:54.753Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "poetry": true, "retrospection": true}, "updated_at": {"__time__": "2023-02-02T05:45:58.978Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-02-02T05:45:58.978Z"}}, "__collections__": {}}, "rjPRC2Kubi2kQsY3loIv": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-16T02:34:55.078Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-16T02:34:55.078Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-16T02:34:55.078Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-16T02:36:06.245Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Chacun de nous est une foule.", "reference": {"id": "Yj5cEusYhyvzqCyFofGZ", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-16T02:35:57.260Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "OkCupid", "release": {"before_common_era": false, "original": {"__time__": "2021-05-16T02:35:57.260Z"}}, "summary": "OkCupid is an American-based, internationally operating online dating, friendship, and social networking website that features multiple-choice questions in order to match members. It is supported by advertisements, by paying users who do not see ads, and by selling user data for data mining.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fokcupid.png?alt=media&token=a8bef069-60b7-4b77-8e9d-9eebb5e821f0", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.okcupid.com/", "wikipedia": "https://en.wikipedia.org/wiki/OkCupid", "youtube": ""}}, "topics": {"social": true}, "updated_at": {"__time__": "2021-05-16T02:36:06.245Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-16T02:36:06.245Z"}}, "__collections__": {}}, "rxIT1BttBnsRWWidvFcf": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T12:34:33.435Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-31T12:34:33.435Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T12:34:33.435Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Aristote ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-31T12:39:30.508Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Le rôle du poète est de dire non pas ce qui a lieu réellement mais ce qui pourrait avoir lieu dans l'ordre du vraisemblable ou du nécessaire.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-31T12:39:17.001Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "La poétique ", "release": {"before_common_era": false, "original": {"__time__": "2023-07-31T12:39:17.001Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "knowledge": true, "poetry": true}, "updated_at": {"__time__": "2023-07-31T12:39:30.508Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-31T12:39:30.508Z"}}, "__collections__": {}}, "sUfMzZXlGCOk2Q9kCWb3": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T12:23:57.828Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-26T12:23:57.828Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T12:23:57.828Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON><PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-26T12:30:09.708Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Tout ce qui est dit au lieu d'être montré est perdu pour le public.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-26T12:23:57.829Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Construire un récit ", "release": {"before_common_era": false, "original": {"__time__": "2023-07-26T12:23:57.829Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"knowledge": true}, "updated_at": {"__time__": "2023-07-26T12:30:09.708Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-26T12:30:09.708Z"}}, "__collections__": {}}, "sg98GDqaIEHLiTOto0g5": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-15T19:42:00.379Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-15T19:42:00.379Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-15T19:42:00.379Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-15T19:43:40.366Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Fils de patate cuisinée.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-15T19:42:00.380Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Atypical", "release": {"before_common_era": false, "original": {"__time__": "2021-07-15T19:42:00.380Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"fun": true}, "updated_at": {"__time__": "2021-07-15T19:43:40.366Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-15T19:43:40.366Z"}}, "__collections__": {}}, "t63BzZzhw7PYCiu3KHBU": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-06-13T09:27:42.962Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-06-13T09:27:42.962Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-13T09:27:42.962Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Emilia", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-06-13T09:29:51.326Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "C'est plus satisfaisant d'entendre un unique \"merci\" que beaucoup de \"désolé\".", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-06-13T09:27:42.963Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Re:zero", "release": {"before_common_era": false, "original": {"__time__": "2021-06-13T09:27:42.963Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true}, "updated_at": {"__time__": "2021-06-13T09:29:51.326Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-06-13T09:29:51.326Z"}}, "__collections__": {}}, "uKJ3qXR02JUxT5qIj3Y7": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-20T10:22:47.278Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-20T10:22:47.278Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-20T10:22:47.278Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-05-20T10:26:45.299Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Vous vivrez peut-être suffisamment longtemps pour voir des horreurs humaines au-delà de votre compréhension.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-20T10:22:47.278Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Fireship", "release": {"before_common_era": false, "original": {"__time__": "2023-05-20T10:22:47.278Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"psychology": true, "social": true, "travel": true}, "updated_at": {"__time__": "2023-05-20T10:26:45.299Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-05-20T10:26:45.299Z"}}, "__collections__": {}}, "uc0leIEkWeVEzAOjbc3k": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-05T15:45:25.150Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-05T15:45:25.150Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-05T15:45:25.150Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-05-05T15:46:58.992Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Science does not break magic, it reveals it.", "reference": {"id": "yTLZPjfPDD07AD9ZUq2O", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-05T15:46:19.273Z"}, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "<PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2023-05-05T15:46:19.273Z"}}, "summary": "Balade Mentale is a french YouTube channel exploring the Universe in poetry. It explores topics like the ending of the Universe, stars' lifespan, or the Library of Babel.", "urls": {"amazon": "", "facebook": "", "image": "https://yt3.ggpht.com/a/AATXAJyIaTK7HHmPeyfI35MF7EEG3yrMw43vazxx8A=s288-c-k-c0xffffffff-no-rj-mo", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "https://www.youtube.com/channel/UCS_7tplUgzJG4DhA16re5Yg/featured", "wikipedia": "", "youtube": ""}}, "topics": {"poetry": true, "sciences": true}, "updated_at": {"__time__": "2023-05-05T15:46:58.992Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-05-05T15:46:58.992Z"}}, "__collections__": {}}, "ueXo6TK0Slq8RJstbvo6": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T22:02:46.721Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T22:02:46.721Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Emannuel <PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-29T22:03:24.105Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "This I can declare...things that are in heaven are more real than things that are in the world.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Things heard and seen", "release": {"before_common_era": false, "original": {"__time__": "2021-05-29T21:59:21.354Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"spiritual": true}, "updated_at": {"__time__": "2021-05-29T22:03:24.105Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-29T22:03:24.105Z"}}, "__collections__": {}}, "uyPrC7bzTXzI7nlsoI9j": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-03T00:21:09.182Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-07-03T00:21:09.182Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-03T00:21:09.182Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON>'s mother", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-07-03T00:22:45.779Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "What matters is not how start. It's how it ends.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-07-03T00:21:09.182Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Re:zero", "release": {"before_common_era": false, "original": {"__time__": "2021-07-03T00:21:09.182Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"psychology": true}, "updated_at": {"__time__": "2021-07-03T00:22:45.779Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-07-03T00:22:45.779Z"}}, "__collections__": {}}, "vNwoNG55SORn9SQHuBzz": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-14T23:56:42.612Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-01-14T23:56:42.612Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-14T23:56:42.612Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Sartre", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-01-14T23:57:23.800Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "He'll is other people.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-01-14T23:56:42.612Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Wednesday", "release": {"before_common_era": false, "original": {"__time__": "2023-01-14T23:56:42.612Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "introspection": true, "social": true}, "updated_at": {"__time__": "2023-01-14T23:57:23.800Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-01-14T23:57:23.800Z"}}, "__collections__": {}}, "vh5j9Q8tHAy4N2E6YjgF": {"author": {"birth": {"before_common_era": false, "city": "Alençon, Normandy", "country": "France", "date": {"__time__": "2021-10-24T23:06:19.065Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-10-24T23:06:19.065Z"}}, "from_reference": {"id": "", "name": ""}, "id": "tfcJHq7qiyPsN0EZMqUP", "image": {"credits": {"artist": "<PERSON>", "before_common_era": false, "company": "", "date": {"__time__": "2018-12-31T23:00:00.000Z"}, "location": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON> 2019", "url": "https://en.wikipedia.org/wiki/Orelsan?oldformat=true#/media/File:Or<PERSON><PERSON>_Deauville_2019.jpg"}}, "is_fictional": false, "job": "French rapper", "name": "<PERSON><PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON><PERSON>, better known by his stage name <PERSON><PERSON><PERSON>, sometimes stylized as <PERSON><PERSON><PERSON><PERSON>, is a French rapper, songwriter, record producer, actor and film director.", "urls": {"amazon": "", "facebook": "https://www.facebook.com/orelsan", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2FOrelsan-1619990138849.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "https://www.instagram.com/orelsan/", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/Orel_San", "website": "https://orelsan.tv/music/", "wikipedia": "https://en.wikipedia.org/wiki/<PERSON><PERSON>an", "youtube": "https://www.youtube.com/channel/UCEnFzIYw3BrndPCddyQ6c5A"}}, "created_at": {"__time__": "2021-10-24T23:07:16.293Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "J'pensais me lever un matin, être un homme.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-10-24T23:05:43.720Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": " San (La fête est finie)", "release": {"before_common_era": false, "original": {"__time__": "2016-12-31T23:00:00.000Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true}, "updated_at": {"__time__": "2021-10-24T23:07:16.293Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-10-24T23:07:16.293Z"}}, "__collections__": {}}, "vyexU2uHiwcs8Sqc6pSk": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-02-10T11:26:54.305Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-02-10T11:26:54.305Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-10T11:26:54.305Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-02-10T11:30:57.006Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Man is worth more than the world because he harvests the nature and builds tools to optimize his world.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-02-10T11:26:54.305Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Les Revues du Monde", "release": {"before_common_era": false, "original": {"__time__": "2023-02-10T11:26:54.305Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"biology": true, "introspection": true, "knowledge": true, "social": true}, "updated_at": {"__time__": "2023-02-10T11:30:57.006Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-02-10T11:30:57.006Z"}}, "__collections__": {}}, "vyo16kUID9yQ1lHqfsZV": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-06-07T19:15:29.519Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-07T19:15:29.519Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "Chewbacca", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-06-07T19:17:48.564Z"}, "language": "en", "name": "AARRWWWRRAGGGGAAWWWWWW", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-06-07T19:15:29.519Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Star Wars A new hope", "release": {"before_common_era": false, "original": {"__time__": "2024-06-07T19:15:29.519Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-06-07T19:17:49.666Z"}, "user": {"id": "p4PWrK73GUWam8wxaOmE64zKFR52"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-06-07T19:17:48.564Z"}}, "status": "", "updated_at": {"__time__": "2024-06-07T19:17:48.564Z"}}, "__collections__": {}}, "wSDqTdMwdDJtT2xcRsQt": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-05-17T22:52:11.154Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-05-17T22:52:11.154Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-05-17T22:48:35.423Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-05-17T22:52:28.336Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "La seule chose équitablement ré<PERSON><PERSON>, c'est l'injustice de l'existence.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-05-17T22:48:35.423Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "<PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": {"__time__": "2022-05-17T22:48:35.423Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"philosophy": true, "social": true, "spiritual": true}, "updated_at": {"__time__": "2022-05-17T22:52:28.336Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-05-17T22:52:28.336Z"}}, "__collections__": {}}, "wYSx94UvAk789bMFEXXw": {"author": {"born": {"before_common_era": false, "city": "", "country": ""}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2024-08-03T23:49:13.585Z"}}, "from_reference": {"id": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-03T23:49:13.585Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "job_localized": {}, "name": "<PERSON> ", "summary": "", "summary_localized": {}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2024-08-03T23:55:34.804Z"}, "language": "en", "name": "When you have nothing left. That’s when it’s go time.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2024-08-03T23:49:13.585Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "", "release": {"before_common_era": false, "original": {"__time__": "2024-08-03T23:49:13.585Z"}}, "summary": "", "type": {"primary": "", "secondary": ""}, "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {}, "updated_at": {"__time__": "2024-08-03T23:55:42.831Z"}, "user": {"id": "iRo6FOKzB4MmOrEUd6wawNZPyuJ2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": {"__time__": "2024-08-03T23:55:34.805Z"}}, "status": "", "updated_at": {"__time__": "2024-08-03T23:55:34.805Z"}}, "__collections__": {}}, "xZzDWVc7YtmyHtIZorzi": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": null}, "death": {"before_common_era": false, "city": "", "country": "", "date": null}, "from_reference": {"id": "", "name": ""}, "id": "nV81YI5rA2A5r4hN2SUx", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "Streamer", "name": "mistermv", "summary": "<PERSON><PERSON><PERSON>, known as mister<PERSON><PERSON>, is a french streamer, video content producer, presentator and music artist. He's specialized in video games and speedrun. He started music in the 1990s and his streaming career in the early 2010s.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fmistermv-1604580113084.jpg?alt=media", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "https://www.twitch.tv/mistermv", "twitter": "https://twitter.com/mistermv", "website": "https://quiveutdesgoodies.com/", "wikipedia": "https://fr.wikipedia.org/wiki/<PERSON>_<PERSON>", "youtube": "https://www.youtube.com/user/monsieurmv"}}, "created_at": {"__time__": "2021-02-26T18:20:24.714Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "test de ouf", "reference": {"id": "yAoD2dzoSrbb68eOm6oQ", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": null, "location": "", "name": "", "url": ""}}, "language": "fr", "name": "<PERSON><PERSON><PERSON>", "release": {"before_common_era": false, "original": null}, "summary": "Clararunaway is a french YouTube channel speaking about cinema and TV shows. From fun facts about Interstellar to how to deal with the death of an actor in a production, the channel cover different aspects of this art.", "urls": {"amazon": "", "facebook": "", "image": "https://firebasestorage.googleapis.com/v0/b/memorare-98eee.appspot.com/o/images%2Fpp%2Fclararunaway.jpg?alt=media&token=ccb17c73-7ad1-43ec-956c-fd1029cb2693", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "https://twitter.com/clararunaway", "website": "", "wikipedia": "", "youtube": "https://www.youtube.com/c/clararunaway/"}}, "topics": {"motivation": true}, "updated_at": {"__time__": "2021-02-26T18:20:24.714Z"}, "user": {"id": "3BWs1FB0zxdXqO7Ccs6YM062rgm2"}, "validation": {"comment": {"moderator_id": "", "name": "La citation est rejetée car elle contient un contenu non pertinent.", "updated_at": null}, "status": "rejected", "updated_at": {"__time__": "2021-03-02T23:26:45.814Z"}}, "__collections__": {}}, "xieSq436vPIZQTeyEESm": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-31T22:01:21.749Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-05-31T22:01:21.749Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-31T22:01:21.749Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-05-31T22:03:49.359Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "You should always seek out proof and you sold never stop asking questions.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-05-31T22:01:21.749Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Pantheon", "release": {"before_common_era": false, "original": {"__time__": "2023-05-31T22:01:21.749Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "psychology": true}, "updated_at": {"__time__": "2023-05-31T22:03:49.359Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-05-31T22:03:49.359Z"}}, "__collections__": {}}, "xyRoOWX011MQvnWEdCpC": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-28T10:39:20.845Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-28T10:39:20.845Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-28T10:39:20.845Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON> ", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-28T10:41:04.073Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "We're probably talking about sex more than anything else.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-28T10:39:20.845Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "arte", "release": {"before_common_era": false, "original": {"__time__": "2023-07-28T10:39:20.845Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true, "language": true, "philosophy": true, "social": true}, "updated_at": {"__time__": "2023-07-28T10:41:04.073Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-28T10:41:04.073Z"}}, "__collections__": {}}, "y16bvrO3Jp2eG8sGr6zJ": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T22:02:46.721Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-05-29T22:02:46.721Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "Emannuel <PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-05-29T22:06:04.520Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Je peux affirmer...que les choses du monde spirituel sont plus réelles que celles du monde naturel.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-05-29T21:59:21.354Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Things heard and seen", "release": {"before_common_era": false, "original": {"__time__": "2021-05-29T21:59:21.354Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"spiritual": true}, "updated_at": {"__time__": "2021-05-29T22:06:04.520Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-05-29T22:06:04.520Z"}}, "__collections__": {}}, "y8dqq9iz2jLjqeVe4HuT": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-02-20T00:21:30.907Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Nos fins dépassent notre imagination.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-02-19T22:43:23.640Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Prestige", "release": {"before_common_era": false, "original": {"__time__": "2022-02-19T22:43:23.640Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "work": true}, "updated_at": {"__time__": "2022-02-20T00:21:30.907Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-02-20T00:21:30.907Z"}}, "__collections__": {}}, "yAK9V7eDnI85GUnqQ6UI": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-22T04:03:59.589Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-03-22T04:03:59.589Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-22T04:00:55.537Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-03-22T04:04:30.635Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "I want to die peacefully in my sleep like my grandfather, not screaming in terror like his passengers.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-03-22T04:00:55.537Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Don't Look Up", "release": {"before_common_era": false, "original": {"__time__": "2023-03-22T04:00:55.537Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"introspection": true, "spiritual": true}, "updated_at": {"__time__": "2023-03-22T04:04:30.635Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-03-22T04:04:30.635Z"}}, "__collections__": {}}, "yg6RoBGFi5awWM0WCZlL": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-09-04T18:53:59.658Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "If at least you do succeed, never try again.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-04T18:52:30.707Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Predestination", "release": {"before_common_era": false, "original": {"__time__": "2021-09-04T18:52:30.707Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"work": true}, "updated_at": {"__time__": "2021-09-04T18:53:59.658Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-09-04T18:53:59.658Z"}}, "__collections__": {}}, "zIcoULBH1KOmL4uhDszw": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-10-11T01:09:16.269Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2022-10-11T01:09:16.269Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "<PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2022-10-11T01:18:06.599Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "Death is just getting out of one car and into another.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2022-10-11T00:55:00.097Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "The Midnight Club", "release": {"before_common_era": false, "original": {"__time__": "2022-10-11T00:55:00.097Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"metaphor": true}, "updated_at": {"__time__": "2022-10-11T01:18:06.599Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2022-10-11T01:18:06.599Z"}}, "__collections__": {}}, "zixAnzPNVMcQwX08K28J": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-01T14:08:31.633Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2023-07-01T14:08:31.633Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-01T14:08:31.633Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "", "name": "", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2023-07-01T14:10:15.758Z"}, "language": "fr", "metrics": {"likes": 0, "shares": 0}, "name": "Trois choses qui arrivent sans son demande : la peur, l'amour et la jalousie.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2023-07-01T14:08:31.633Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "L'Île des chasseurs d'oiseaux ", "release": {"before_common_era": false, "original": {"__time__": "2023-07-01T14:08:31.633Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"feelings": true}, "updated_at": {"__time__": "2023-07-01T14:10:15.758Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2023-07-01T14:10:15.758Z"}}, "__collections__": {}}, "zom42Pr50IsCBd9Dn7bZ": {"author": {"birth": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-19T03:00:03.046Z"}}, "death": {"before_common_era": false, "city": "", "country": "", "date": {"__time__": "2021-09-19T03:00:03.046Z"}}, "from_reference": {"id": "", "name": ""}, "id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-19T03:00:03.046Z"}, "location": "", "name": "", "url": ""}}, "is_fictional": false, "job": "Pilote de ligne", "name": "<PERSON><PERSON>", "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "created_at": {"__time__": "2021-09-19T03:02:37.573Z"}, "language": "en", "metrics": {"likes": 0, "shares": 0}, "name": "One problem at a time.", "reference": {"id": "", "image": {"credits": {"artist": "", "before_common_era": false, "company": "", "date": {"__time__": "2021-09-19T03:00:03.046Z"}, "location": "", "name": "", "url": ""}}, "language": "en", "name": "Into the Night", "release": {"before_common_era": false, "original": {"__time__": "2021-09-19T03:00:03.046Z"}}, "summary": "", "urls": {"amazon": "", "facebook": "", "image": "", "image_name": "", "imdb": "", "instagram": "", "netflix": "", "prime_video": "", "twitch": "", "twitter": "", "website": "", "wikipedia": "", "youtube": ""}}, "topics": {"psychology": true}, "updated_at": {"__time__": "2021-09-19T03:02:37.573Z"}, "user": {"id": "PqFZbBTyPpQHPoeHO9jjnmDAbLE2"}, "validation": {"comment": {"moderator_id": "", "name": "", "updated_at": null}, "status": "", "updated_at": {"__time__": "2021-09-19T03:02:37.573Z"}}, "__collections__": {}}}}